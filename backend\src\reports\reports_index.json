[{"id": "9d8fb2b1-d7c1-441c-9a77-48ec7080efdf", "template_id": "joint_report", "template_name": "Joint Report Template", "form_data": {"LocoFailureOrDetention": "khkhk", "Date": "2025-05-12", "LocoNo": "khk", "Shed": "hk", "SchDone": "hkh", "SchDue": "kh", "TrainNo": "kh", "Load": "khk", "LPM": "hkh", "ALP": "khk", "Section": "hkhjkj", "Station": "kjkjk", "Supervisor1": "kjkjkj", "Supervisor2": "hhi<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "docx_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\9d8fb2b1-d7c1-441c-9a77-48ec7080efdf.docx", "pdf_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\9d8fb2b1-d7c1-441c-9a77-48ec7080efdf.pdf", "created_at": "2025-08-08T12:22:01.539667", "e_signed": false}, {"id": "0bbb2cb1-b38c-4c47-9baf-293d7eda53f0", "template_id": "joint_report", "template_name": "Joint Report Template", "form_data": {"LocoFailureOrDetention": "khkhk", "Date": "2025-05-12", "LocoNo": "khk", "Shed": "hk", "SchDone": "hkh", "SchDue": "kh", "TrainNo": "kh", "Load": "khk", "LPM": "hkh", "ALP": "khk", "Section": "hkhjkj", "Station": "kjkjk", "Supervisor1": "ghg", "Supervisor2": "hg", "BriefHistory": "khhk\ndf\ndg\ngdg\ngd\ng", "Investigation": "hk\ngd\ngdgdggdvvd\nvdvd\nv'dv", "Conclusion": "kh\nvd\nd\ndvd\ndv\n", "Responsibility": "djfdljfd\nfdv\nvdvd"}, "docx_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\0bbb2cb1-b38c-4c47-9baf-293d7eda53f0.docx", "pdf_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\0bbb2cb1-b38c-4c47-9baf-293d7eda53f0.pdf", "created_at": "2025-08-08T14:44:36.140406", "e_signed": false}, {"id": "aeeb95db-02e8-41e5-b722-bfc7cd24f5f7", "template_id": "joint_report", "template_name": "Joint Report Template", "form_data": {"LocoFailureOrDetention": "khkhk", "Date": "2025-05-12", "LocoNo": "khk", "Shed": "hk", "SchDone": "hkh", "SchDue": "kh", "TrainNo": "kh", "Load": "khk", "LPM": "hkh", "ALP": "khk", "Section": "hkhjkj", "Station": "kjkjk", "Supervisor1": "ghg", "Supervisor2": "hg"}, "docx_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\aeeb95db-02e8-41e5-b722-bfc7cd24f5f7.docx", "pdf_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\aeeb95db-02e8-41e5-b722-bfc7cd24f5f7_signed_1754660108.pdf", "created_at": "2025-08-08T18:37:56.301102", "e_signed": true, "signatures_data": [{"signature": "data:image/png;base64,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", "position": {"x": 46.949422491360465, "y": 38.49929339506822}, "size": {"width": 98.49789936898274, "height": 33.954659749410396}, "page": 1}], "signed_at": "2025-08-08T19:05:08.949647", "document_txt_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\documents\\aeeb95db-02e8-41e5-b722-bfc7cd24f5f7.txt", "document_last_saved_at": "2025-08-08T19:24:50.128996"}, {"id": "eac42e5f-d5d9-43a4-87a8-ce9477917b88", "template_id": "joint_report", "template_name": "Joint Report Template", "form_data": {"LocoFailureOrDetention": "khkhk", "Date": "2025-05-12", "LocoNo": "khk", "Shed": "hk", "SchDone": "hkh", "SchDue": "kh", "TrainNo": "kh", "Load": "khk", "LPM": "hkh", "ALP": "khk", "Section": "hkhjkj", "Station": "kjkjk", "Supervisor1": "kjkjkj", "Supervisor2": "hhi<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "docx_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\eac42e5f-d5d9-43a4-87a8-ce9477917b88.docx", "pdf_path": "C:\\IR APP RailGPT\\report-generator-application\\backend\\src\\reports\\eac42e5f-d5d9-43a4-87a8-ce9477917b88.pdf", "created_at": "2025-08-09T18:54:39.368263", "e_signed": false}, {"id": "d0227476-13f7-4da2-82f7-5c76da2f69db", "template_id": "joint_report", "template_name": "Joint Report Template", "form_data": {"LocoFailureOrDetention": "fsf", "Date": "1995-06-12", "LocoNo": "b  f", "Shed": "ffwdf", "SchDone": "WRRET", "SchDue": "R", "TrainNo": "R", "Load": "FG", "LPM": "G", "ALP": "G", "Section": "RR", "Station": "RTT", "BriefHistory": "TT", "Investigation": "TT", "Conclusion": "TT", "Responsibility": "T", "Supervisor1": "TGGH"}, "docx_path": "C:\\Users\\<USER>\\docmaker\\backend\\src\\reports\\d0227476-13f7-4da2-82f7-5c76da2f69db.docx", "pdf_path": "C:\\Users\\<USER>\\docmaker\\backend\\src\\reports\\d0227476-13f7-4da2-82f7-5c76da2f69db_signed_1754822061.pdf", "created_at": "2025-08-10T16:03:39.266174", "e_signed": true, "signatures_data": [{"signature": "data:image/png;base64,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", "position": {"x": 10.266821345707656, "y": 37.67313019390582}, "size": {"width": 116.12529002320186, "height": 34.51523545706371}, "page": 1}], "signed_at": "2025-08-10T16:04:21.641858"}]