nohup: ignoring input
 * Serving Flask app 'main'
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
[33mPress CTRL+C to quit[0m
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 773-026-480
127.0.0.1 - - [03/Aug/2025 10:38:05] "[36mGET / HTTP/1.1[0m" 304 -
127.0.0.1 - - [03/Aug/2025 10:38:05] "[36mGET /assets/index-CT5LdcHg.css HTTP/1.1[0m" 304 -
127.0.0.1 - - [03/Aug/2025 10:38:05] "[36mGET /assets/index-nbwEvFo_.js HTTP/1.1[0m" 304 -
127.0.0.1 - - [03/Aug/2025 10:38:05] "[36mGET /assets/index-CT5LdcHg.css HTTP/1.1[0m" 304 -
127.0.0.1 - - [03/Aug/2025 10:38:05] "GET /api/templates HTTP/1.1" 200 -
127.0.0.1 - - [03/Aug/2025 10:55:18] "GET /api/reports HTTP/1.1" 200 -
127.0.0.1 - - [03/Aug/2025 10:55:18] "GET /api/templates HTTP/1.1" 200 -
127.0.0.1 - - [03/Aug/2025 10:56:44] "GET /api/templates HTTP/1.1" 200 -
127.0.0.1 - - [04/Aug/2025 00:51:54] "GET /api/templates/ta_form/placeholders HTTP/1.1" 200 -
127.0.0.1 - - [04/Aug/2025 00:55:51] "POST /api/reports/generate HTTP/1.1" 200 -
127.0.0.1 - - [04/Aug/2025 00:55:58] "GET /api/reports/9f6db8ba-c8fd-496b-b91b-3b3c118f28ad/download/docx HTTP/1.1" 200 -
127.0.0.1 - - [04/Aug/2025 00:56:02] "GET /api/reports/9f6db8ba-c8fd-496b-b91b-3b3c118f28ad/download/pdf HTTP/1.1" 200 -
