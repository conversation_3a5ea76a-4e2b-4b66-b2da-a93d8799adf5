import json
import os
import tempfile
import time
from typing import Any

import portalocker


# Resolve paths relative to src/ directory
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
REPORTS_INDEX_PATH = os.path.join(REPORTS_DIR, 'reports_index.json')
LOCK_PATH = REPORTS_INDEX_PATH + '.lock'


def _ensure_reports_dir_exists() -> None:
    os.makedirs(REPORTS_DIR, exist_ok=True)


def load_reports_index() -> Any:
    """Load and parse the reports index with a shared lock.

    Returns an empty list if the file does not exist or is empty/invalid.
    """
    _ensure_reports_dir_exists()
    if not os.path.exists(REPORTS_INDEX_PATH):
        return []

    # Acquire a shared lock for reading using a sidecar lock file
    try:
        with portalocker.Lock(LOCK_PATH, mode='a+', flags=portalocker.LOCK_SH, timeout=10):
            with open(REPORTS_INDEX_PATH, 'r', encoding='utf-8') as file_handle:
                try:
                    data = json.load(file_handle)
                except Exception:
                    return []
                return data if isinstance(data, (list, dict)) else []
    except Exception:
        # On any lock/read/parse error, treat as empty
        return []


def save_reports_index(data: Any) -> None:
    """Atomically write the reports index with an exclusive lock.

    - Acquires an exclusive lock on the final JSON file
    - Writes to a temporary file in the same directory
    - fsyncs and replaces the destination (atomic on POSIX and Windows 10+)
    """
    _ensure_reports_dir_exists()

    # Ensure the target file exists so readers don't race on non-existence
    if not os.path.exists(REPORTS_INDEX_PATH):
        with open(REPORTS_INDEX_PATH, 'w', encoding='utf-8') as fh:
            fh.write('[]')

    # Use a sidecar lock file for cross-platform exclusive locking during write
    with portalocker.Lock(LOCK_PATH, mode='a+', flags=portalocker.LOCK_EX, timeout=30):
        # Write to a temp file within the same directory
        fd, temp_path = tempfile.mkstemp(prefix='reports_index.', suffix='.json.tmp', dir=REPORTS_DIR, text=True)
        try:
            with os.fdopen(fd, 'w', encoding='utf-8') as tmp_f:
                json.dump(data, tmp_f, ensure_ascii=False, indent=2)
                tmp_f.flush()
                os.fsync(tmp_f.fileno())
            # Replace destination atomically
            os.replace(temp_path, REPORTS_INDEX_PATH)
        finally:
            # If something failed before replace, remove temp file
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except OSError:
                    pass


def _concurrency_smoke_test(iterations: int = 100, writers: int = 4) -> None:
    """Simple multi-process stress test to ensure no JSON corruption occurs.

    The test does not guarantee no lost updates (last writer wins is acceptable),
    it only checks that the file remains valid JSON of list/dict shape under
    concurrent writes.
    """
    import multiprocessing as mp

    def writer_proc(proc_id: int, num_iters: int) -> None:
        for i in range(num_iters):
            # Produce some data with a timestamp and id to simulate activity
            payload = {
                'proc': proc_id,
                'seq': i,
                't': time.time(),
            }
            # Try read/modify/write pattern; acceptable if last-writer-wins
            current = load_reports_index()
            if isinstance(current, list):
                current.append(payload)
            else:
                # If dict, store/overwrite by key
                current = {'last': payload}
            save_reports_index(current)

    # Start from a clean file
    try:
        os.remove(REPORTS_INDEX_PATH)
    except FileNotFoundError:
        pass

    procs: list[mp.Process] = []
    for pid in range(writers):
        p = mp.Process(target=writer_proc, args=(pid, iterations))
        p.start()
        procs.append(p)

    # Also perform reads while writers are active
    for _ in range(iterations):
        data = load_reports_index()
        # Validate JSON shape
        if not isinstance(data, (list, dict)):
            raise AssertionError('Corrupted JSON: not a list or dict')
        time.sleep(0.005)

    for p in procs:
        p.join(timeout=30)
        assert not p.is_alive(), 'A writer process did not exit in time'

    # Final sanity read
    data = load_reports_index()
    if not isinstance(data, (list, dict)):
        raise AssertionError('Final JSON corrupted')


if __name__ == '__main__':
    print('Running reports_index concurrency smoke test...')
    _concurrency_smoke_test(iterations=50, writers=4)
    print('OK: no corruption observed under concurrent writes.')




