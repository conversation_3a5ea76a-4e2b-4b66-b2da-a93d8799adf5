from flask import Blueprint, jsonify, request
from src.models.user import User, db
from src.utils.audit import audit_event
from src.utils.errors import error_response
from pydantic import BaseModel, Field
from typing import Optional

user_bp = Blueprint('user', __name__)

@user_bp.route('/users', methods=['GET'])
def get_users():
    users = User.query.all()
    return jsonify([user.to_dict() for user in users])

class CreateUserRequest(BaseModel):
    username: str = Field(min_length=3, max_length=50)
    email: str = Field(min_length=5, max_length=254)


@user_bp.route('/users', methods=['POST'])
def create_user():
    payload = CreateUserRequest.model_validate(request.get_json(silent=True) or {})
    user = User(username=payload.username, email=payload.email)
    db.session.add(user)
    db.session.commit()
    try:
        audit_event('user_created', 'anonymous', {'user_id': user.id, 'username': user.username})
    except Exception:
        pass
    return jsonify(user.to_dict()), 201

@user_bp.route('/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return error_response(404, 'User not found')
    return jsonify(user.to_dict())

class UpdateUserRequest(BaseModel):
    username: Optional[str] = Field(default=None, min_length=3, max_length=50)
    email: Optional[str] = Field(default=None, min_length=5, max_length=254)


@user_bp.route('/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return error_response(404, 'User not found')
    payload = UpdateUserRequest.model_validate(request.get_json(silent=True) or {})
    if payload.username is not None:
        user.username = payload.username
    if payload.email is not None:
        user.email = payload.email
    db.session.commit()
    try:
        audit_event('user_updated', 'anonymous', {'user_id': user.id})
    except Exception:
        pass
    return jsonify(user.to_dict())

@user_bp.route('/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    user = User.query.get(user_id)
    if not user:
        return error_response(404, 'User not found')
    db.session.delete(user)
    db.session.commit()
    try:
        audit_event('user_deleted', 'anonymous', {'user_id': user_id})
    except Exception:
        pass
    return '', 204
