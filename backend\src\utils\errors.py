import logging
from typing import Any, Dict, Optional, <PERSON>ple

from flask import jsonify
from werkzeug.exceptions import HTTPException

try:  # Prefer Pydantic v2
    from pydantic import ValidationError
except Exception:  # pragma: no cover - fallback type for environments without pydantic
    class ValidationError(Exception):  # type: ignore
        pass


logger = logging.getLogger(__name__)


def error_response(status_code: int, message: str, details: Optional[Dict[str, Any]] = None):
    """Create a consistent JSON error response.

    Shape: { "error": { "message": str, "details": dict | null } }
    Returns a Flask response with the given status code.
    """
    payload: Dict[str, Any] = {"error": {"message": message}}
    if details is not None:
        payload["error"]["details"] = details
    else:
        payload["error"]["details"] = None
    return jsonify(payload), status_code


def _format_pydantic_errors(exc: ValidationError) -> Dict[str, Any]:
    """Normalize Pydantic validation errors for API responses."""
    try:
        # Pydantic v2
        return {"issues": exc.errors()}
    except Exception:
        # Best effort
        return {"message": str(exc)}


def register_error_handlers(app) -> None:
    """Register global exception handlers on the Flask app.

    - HTTPException → formatted into unified JSON
    - Pydantic ValidationError → 422 with validation details
    - Any other Exception → 500 generic message with full traceback logged
    """

    @app.errorhandler(HTTPException)
    def handle_http_exception(error: HTTPException):  # type: ignore
        # Log with traceback; even expected HTTP errors are recorded for observability
        logger.exception("HTTPException: %s", error)
        message = error.description or error.name or "HTTP error"
        details = {"type": error.__class__.__name__, "code": getattr(error, "code", None)}
        status_code = getattr(error, "code", 500) or 500
        return error_response(status_code, message, details)

    @app.errorhandler(ValidationError)
    def handle_validation_error(error: ValidationError):  # type: ignore
        logger.exception("ValidationError: %s", error)
        details = _format_pydantic_errors(error)
        return error_response(422, "Request validation failed", details)

    @app.errorhandler(Exception)
    def handle_unexpected_exception(error: Exception):  # type: ignore
        # Capture full traceback but do not leak internal details in the response
        logger.exception("Unhandled exception: %s", error)
        return error_response(500, "Internal server error" )


if __name__ == "__main__":
    # Minimal smoke test for the helpers (does not start a server)
    print("Testing error response structure:")
    
    # Test the payload structure without Flask context
    payload = {"error": {"message": "Bad request", "details": {"field": "value"}}}
    print(f"Sample payload: {payload}")
    
    # Test validation error formatting if pydantic is installed
    try:
        from pydantic import BaseModel, Field

        class TestModel(BaseModel):
            name: str = Field(min_length=2)
            age: int = Field(ge=0)

        try:
            TestModel.model_validate({"name": "A", "age": -1})  # type: ignore[attr-defined]
        except ValidationError as ve:  # noqa: F401
            details = _format_pydantic_errors(ve)
            print(f"Validation error details: {details}")
    except Exception as e:
        print(f"Pydantic test skipped: {e}")
    
    print("Error utility module test completed successfully!")


