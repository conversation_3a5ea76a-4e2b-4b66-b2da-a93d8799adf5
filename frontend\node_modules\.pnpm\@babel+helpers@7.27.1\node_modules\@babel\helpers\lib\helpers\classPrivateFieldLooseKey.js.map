{"version": 3, "names": ["id", "_classPrivateFieldKey", "name"], "sources": ["../../src/helpers/classPrivateFieldLooseKey.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nvar id = 0;\nexport default function _classPrivateFieldKey(name: string) {\n  return \"__private_\" + id++ + \"_\" + name;\n}\n"], "mappings": ";;;;;;AAEA,IAAIA,EAAE,GAAG,CAAC;AACK,SAASC,qBAAqBA,CAACC,IAAY,EAAE;EAC1D,OAAO,YAAY,GAAGF,EAAE,EAAE,GAAG,GAAG,GAAGE,IAAI;AACzC", "ignoreList": []}