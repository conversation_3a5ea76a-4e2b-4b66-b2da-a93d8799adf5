from flask import Blueprint, jsonify, request, send_file, Response
import requests
import os
import json
import uuid
import base64
from datetime import datetime
import re
import logging
from docx import Document
from docx.shared import Mm
from docx.enum.section import WD_ORIENT
import tempfile
import subprocess
import io
from PIL import Image
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
from src.utils.files import generate_uuid_filename, safe_join
from src.utils.audit import audit_event
from src.utils.reports_index import load_reports_index, save_reports_index
from src.utils.errors import error_response
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator

reports_bp = Blueprint('reports', __name__)
logger = logging.getLogger(__name__)

# Storage paths and persistence helpers
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
DOCUMENTS_DIR = os.path.join(BASE_DIR, 'documents')
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(DOCUMENTS_DIR, exist_ok=True)
REPORTS_INDEX_PATH = os.path.join(REPORTS_DIR, 'reports_index.json')

"""In-memory storage for reports (in production, use a database).
Load from disk if present via concurrency-safe helper.
"""
REPORTS_STORAGE = load_reports_index() or []

def persist_reports_storage():
    try:
        save_reports_index(REPORTS_STORAGE)
    except Exception as e:
        # Non-fatal in dev mode
        logger.warning("Failed to persist reports index: %s", e)

# Load template configuration (IDs and names) from JSON config
CONFIG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
TEMPLATES_CONFIG_PATH = os.path.join(CONFIG_DIR, 'templates.json')
try:
    with open(TEMPLATES_CONFIG_PATH, 'r', encoding='utf-8') as f:
        templates_config = json.load(f)
except Exception:
    templates_config = {}

# Template configurations (placeholders defined in code; IDs and names from config)
TEMPLATES = {
    'ta_form': {
        'name': templates_config.get('ta_form', {}).get('name', 'TA Form 1 Template'),
        'google_doc_id': templates_config.get('ta_form', {}).get('google_doc_id', ''),
        'placeholders': [
            'Branch', 'Division', 'Headquarter', 'NameofEmployee', 'Month',
            'Basic', 'GradePay', 'PFNo', 'MobileNo', 'Designation'
        ] + [f'{field}{i}' for i in range(1, 8) for field in [
            'MonthanDate', 'TrainNo', 'TimeLeft', 'TimeArrived',
            'StationFrom', 'StationTo', 'Kms', 'DayNight', 'ObjectofJourney', 'Rate'
        ]]
    },
    'joint_report': {
        'name': templates_config.get('joint_report', {}).get('name', 'Joint Report Template'),
        'google_doc_id': templates_config.get('joint_report', {}).get('google_doc_id', ''),
        'placeholders': [
            'LocoFailureOrDetention', 'Date', 'LocoNo', 'Shed', 'SchDone', 'SchDue',
            'TrainNo', 'Load', 'LPM', 'ALP', 'Section', 'Station', 'BriefHistory',
            'Investigation', 'Conclusion', 'Responsibility', 'Supervisor1', 'Supervisor2'
        ]
    }
}

# ==========================
# Pydantic request models
# ==========================

class GenerateReportRequest(BaseModel):
    template_id: str = Field(min_length=1)
    form_data: Dict[str, Any] = Field(default_factory=dict)

    @field_validator('template_id')
    @classmethod
    def template_must_exist(cls, v: str) -> str:
        if v not in TEMPLATES:
            raise ValueError('Invalid template_id')
        return v


class GetReportsQuery(BaseModel):
    search: Optional[str] = ''
    template: Optional[str] = None

    @field_validator('template')
    @classmethod
    def template_filter_valid(cls, v: Optional[str]) -> Optional[str]:
        if v and v not in TEMPLATES:
            raise ValueError('Invalid template')
        return v


class SignaturePosition(BaseModel):
    x: float = Field(ge=0, le=100)
    y: float = Field(ge=0, le=100)


class SignatureSize(BaseModel):
    width: float = Field(gt=0)
    height: float = Field(gt=0)


class SignaturePayload(BaseModel):
    signature: str = Field(min_length=10)
    page: int = Field(ge=1, default=1)
    position: SignaturePosition = Field(default_factory=lambda: SignaturePosition(x=50, y=90))
    size: SignatureSize = Field(default_factory=lambda: SignatureSize(width=150, height=60))


class SignReportRequest(BaseModel):
    signatures: List[SignaturePayload] = Field(min_length=1)


class SaveDocumentRequest(BaseModel):
    content: str = ''

def get_template_content_as_docx(template_id):
    """Download Google Doc as DOCX format"""
    if template_id not in TEMPLATES:
        return None
    
    google_doc_id = TEMPLATES[template_id]['google_doc_id']
    export_url = f"https://docs.google.com/document/d/{google_doc_id}/export?format=docx"
    
    try:
        response = requests.get(export_url)
        if response.status_code == 200:
            return response.content
        return None
    except Exception:
        return None

def parse_editor_content(content):
    """Parse markdown sections from editor content into structured data"""
    sections = {}
    if not content:
        return sections
    
    # Split content by markdown headers
    lines = content.split('\n')
    current_section = 'Introduction'
    current_content = []
    
    for line in lines:
        line = line.strip()
        # Check for markdown headers (# ## ### etc.)
        if line.startswith('#'):
            # Save previous section
            if current_content:
                sections[current_section] = '\n'.join(current_content).strip()
            
            # Start new section
            current_section = line.lstrip('#').strip()
            current_content = []
        else:
            if line:  # Skip empty lines at start of sections
                current_content.append(line)
    
    # Save the last section
    if current_content:
        sections[current_section] = '\n'.join(current_content).strip()
    
    # If no sections found, treat entire content as 'Content'
    if not sections and content.strip():
        sections['Content'] = content.strip()
    
    return sections

def replace_placeholders_in_docx(docx_content, form_data, template_id, report_id=None):
    """Replace placeholders in DOCX content with form data and editor content"""
    # Save the content to a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
        temp_file.write(docx_content)
        temp_file_path = temp_file.name
    
    try:
        # Open the document
        doc = Document(temp_file_path)
        
        # Get all placeholders from the template
        template_placeholders = TEMPLATES[template_id]['placeholders'] if template_id in TEMPLATES else []
        
        # Load editor content if available
        editor_content = ''
        editor_sections = {}
        if report_id:
            try:
                doc_path = safe_join(DOCUMENTS_DIR, f'{report_id}.txt')
            except ValueError:
                doc_path = None
            if doc_path and os.path.exists(doc_path):
                try:
                    with open(doc_path, 'r', encoding='utf-8') as f:
                        editor_content = f.read()
                    
                    # Parse markdown sections from editor content
                    editor_sections = parse_editor_content(editor_content)
                except Exception as e:
                    logger.error("Failed to load editor content: %s", e)
        
        # Combine form data with editor content sections for comprehensive replacement
        all_replacements = dict(form_data)
        
        # Map editor sections to common DOCX placeholders
        if editor_sections:
            # Map common section names to standard placeholders
            section_mapping = {
                'Investigation': 'Investigation',
                'Brief History': 'BriefHistory', 
                'Conclusion': 'Conclusion',
                'Content': 'Investigation',  # Default content goes to Investigation
                'Summary': 'Conclusion',
                'Background': 'BriefHistory',
                'Analysis': 'Investigation',
                'Findings': 'Investigation',
                'Recommendations': 'Conclusion'
            }
            
            for section_name, content in editor_sections.items():
                # Direct mapping if section name matches a template placeholder
                if section_name in template_placeholders:
                    all_replacements[section_name] = content
                else:
                    # Use mapping if available
                    mapped_name = section_mapping.get(section_name)
                    if mapped_name and mapped_name in template_placeholders:
                        all_replacements[mapped_name] = content
            
            # If we have editor content but no specific sections, use it for common fields
            if editor_content and not editor_sections:
                if 'Investigation' in template_placeholders:
                    all_replacements['Investigation'] = editor_content
                elif 'Content' in template_placeholders:
                    all_replacements['Content'] = editor_content
        
        # Replace placeholders in paragraphs
        for paragraph in doc.paragraphs:
            # First replace filled placeholders (form data + editor content)
            for placeholder, value in all_replacements.items():
                if f'{{{{{placeholder}}}}}' in paragraph.text and value and str(value).strip():
                    # Handle multiline content by replacing with properly formatted text
                    formatted_value = str(value).replace('\n', '\n')
                    paragraph.text = paragraph.text.replace(f'{{{{{placeholder}}}}}', formatted_value)
            
            # Then remove any remaining empty placeholders
            for placeholder in template_placeholders:
                if f'{{{{{placeholder}}}}}' in paragraph.text:
                    paragraph.text = paragraph.text.replace(f'{{{{{placeholder}}}}}', '')
        
        # Replace placeholders in tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    # First replace filled placeholders (form data + editor content)
                    for placeholder, value in all_replacements.items():
                        if f'{{{{{placeholder}}}}}' in cell.text and value and str(value).strip():
                            # Handle multiline content by replacing with properly formatted text
                            formatted_value = str(value).replace('\n', '\n')
                            cell.text = cell.text.replace(f'{{{{{placeholder}}}}}', formatted_value)
                    
                    # Then remove any remaining empty placeholders
                    for placeholder in template_placeholders:
                        if f'{{{{{placeholder}}}}}' in cell.text:
                            cell.text = cell.text.replace(f'{{{{{placeholder}}}}}', '')
        
        # Enforce page orientation per template (A4)
        try:
            if template_id == 'joint_report':
                for section in doc.sections:
                    section.orientation = WD_ORIENT.PORTRAIT
                    section.page_width = Mm(210)
                    section.page_height = Mm(297)
            elif template_id == 'ta_form':
                for section in doc.sections:
                    section.orientation = WD_ORIENT.LANDSCAPE
                    section.page_width = Mm(297)
                    section.page_height = Mm(210)
        except Exception:
            # Orientation enforcement is best-effort; continue on failure
            pass

        # Save the modified document
        output_path = temp_file_path.replace('.docx', '_filled.docx')
        doc.save(output_path)
        
        # Read the modified content
        with open(output_path, 'rb') as f:
            modified_content = f.read()
        
        # Clean up temporary files
        os.unlink(temp_file_path)
        os.unlink(output_path)
        
        return modified_content
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise e

def convert_docx_to_pdf_libreoffice(docx_content):
    """Convert DOCX content to PDF using LibreOffice"""
    # Save DOCX to temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_docx:
        temp_docx.write(docx_content)
        temp_docx_path = temp_docx.name
    
    temp_dir = None
    
    try:
        # Create temporary directory for output
        temp_dir = tempfile.mkdtemp()
        
        # Find LibreOffice executable path on Windows
        libreoffice_paths = [
            r'C:\Program Files\LibreOffice\program\soffice.exe',
            r'C:\Program Files (x86)\LibreOffice\program\soffice.exe',
            'libreoffice',  # For Linux or if in PATH
            'soffice'       # Alternative command name
        ]
        
        libreoffice_exec = None
        for path in libreoffice_paths:
            if os.path.exists(path) or os.name != 'nt':
                libreoffice_exec = path
                break
        
        if not libreoffice_exec:
            raise Exception("LibreOffice not found. Please install LibreOffice.")
        
        # Convert to PDF using LibreOffice
        try:
            result = subprocess.run([
                libreoffice_exec, '--headless', '--convert-to', 'pdf',
                '--outdir', temp_dir, temp_docx_path
            ], check=True, capture_output=True, text=True)
            
            # For debugging
            logger.debug("LibreOffice conversion output: %s", result.stdout)
            
        except subprocess.CalledProcessError as e:
            logger.error("LibreOffice error: %s", e.stderr)
            raise Exception(f"PDF conversion failed: {e.stderr}")
        
        # Find the generated PDF file
        pdf_filename = os.path.basename(temp_docx_path).replace('.docx', '.pdf')
        temp_pdf_path = os.path.join(temp_dir, pdf_filename)
        
        if not os.path.exists(temp_pdf_path):
            raise Exception(f"PDF file not created at expected path: {temp_pdf_path}")
        
        # Read PDF content
        with open(temp_pdf_path, 'rb') as f:
            pdf_content = f.read()
        
        # Clean up
        os.unlink(temp_docx_path)
        os.unlink(temp_pdf_path)
        os.rmdir(temp_dir)
        
        return pdf_content
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_docx_path):
            os.unlink(temp_docx_path)
        if temp_dir and os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        logger.exception("PDF conversion error: %s", str(e))
        raise Exception(f"Failed to convert DOCX to PDF: {str(e)}")

@reports_bp.route('/reports/generate', methods=['POST'])
def generate_report():
    """Generate a report from template and form data"""
    payload = GenerateReportRequest.model_validate(request.get_json(silent=True) or {})
    template_id = payload.template_id
    form_data = payload.form_data

    # Get template content as DOCX
    docx_content = get_template_content_as_docx(template_id)
    if not docx_content:
        return error_response(502, 'Template content unavailable')

    # Generate unique report ID
    report_id = str(uuid.uuid4())

    # Replace placeholders with form data and editor content
    filled_docx_content = replace_placeholders_in_docx(docx_content, form_data, template_id, report_id)

    # Convert to PDF
    pdf_content = convert_docx_to_pdf_libreoffice(filled_docx_content)

    # Save files with opaque UUID filenames
    reports_dir = REPORTS_DIR
    docx_filename = generate_uuid_filename('.docx')
    pdf_filename = generate_uuid_filename('.pdf')
    docx_path = safe_join(reports_dir, docx_filename)
    pdf_path = safe_join(reports_dir, pdf_filename)

    with open(docx_path, 'wb') as f:
        f.write(filled_docx_content)

    with open(pdf_path, 'wb') as f:
        f.write(pdf_content)

    # Store report metadata
    report_metadata = {
        'id': report_id,
        'template_id': template_id,
        'template_name': TEMPLATES[template_id]['name'],
        'form_data': form_data,
        'docx_path': docx_path,
        'pdf_path': pdf_path,
        'created_at': datetime.now().isoformat(),
        'e_signed': False
    }

    REPORTS_STORAGE.append(report_metadata)
    persist_reports_storage()

    # Audit: report generated
    try:
        audit_event(
            action='report_generated',
            user='anonymous',
            details={'report_id': report_id, 'template_id': template_id}
        )
    except Exception:
        # Never fail the request due to audit logging
        pass

    return jsonify({
        'report_id': report_id,
        'message': 'Report generated successfully',
        'download_urls': {
            'docx': f'/api/reports/{report_id}/download/docx',
            'pdf': f'/api/reports/{report_id}/download/pdf'
        }
    })

@reports_bp.route('/reports', methods=['GET'])
def get_reports():
    """Get list of all generated reports with search and filter"""
    args_model = GetReportsQuery.model_validate(request.args.to_dict())
    search_query = (args_model.search or '').lower()
    template_filter = args_model.template
    
    filtered_reports = []
    
    for report in REPORTS_STORAGE:
        # Apply search filter
        if search_query:
            searchable_text = f"{report['template_name']} {report['id']} {json.dumps(report['form_data'])}".lower()
            if search_query not in searchable_text:
                continue
        
        # Apply template filter
        if template_filter and report['template_id'] != template_filter:
            continue
        
        # Create response object (exclude file paths for security)
        filtered_reports.append({
            'id': report['id'],
            'template_id': report['template_id'],
            'template_name': report['template_name'],
            'created_at': report['created_at'],
            'e_signed': report['e_signed'],
            'download_urls': {
                'docx': f'/api/reports/{report["id"]}/download/docx',
                'pdf': f'/api/reports/{report["id"]}/download/pdf'
            }
        })
    
    return jsonify(filtered_reports)

@reports_bp.route('/reports/<report_id>/download/<format>', methods=['GET'])
def download_report(report_id, format):
    """Download a report in specified format"""
    if format not in ['docx', 'pdf']:
        return error_response(400, 'Invalid format')
    
    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break
    
    if not report:
        return error_response(404, 'Report not found')
    
    file_path = report[f'{format}_path']
    
    if not os.path.exists(file_path):
        return error_response(404, 'File not found')
    
    # Audit: report downloaded
    try:
        audit_event(
            action='report_downloaded',
            user='anonymous',
            details={'report_id': report_id, 'format': format}
        )
    except Exception:
        pass

    return send_file(
        file_path,
        as_attachment=True,
        download_name=f'report_{report_id}.{format}',
        mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document' if format == 'docx' else 'application/pdf'
    )

@reports_bp.route('/reports/<report_id>/sign', methods=['POST'])
def sign_report(report_id):
    """Add multiple e-signatures to a report and embed them in the PDF file"""
    payload = SignReportRequest.model_validate(request.get_json(silent=True) or {})
    signatures_data = [s.model_dump() for s in payload.signatures]

    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break

    if not report:
        return error_response(404, 'Report not found')

    # Get the PDF file path
    pdf_path = report['pdf_path']
    if not os.path.exists(pdf_path):
        return error_response(404, 'PDF file not found')

    # Open existing PDF
    pdf_reader = PdfReader(pdf_path)
    pdf_writer = PdfWriter()

    # Group signatures by page number
    signatures_by_page: Dict[int, List[Dict[str, Any]]] = {}
    for sig_data in signatures_data:
        page_num = sig_data.get('page', 1) - 1  # Convert to 0-based index
        if page_num not in signatures_by_page:
            signatures_by_page[page_num] = []
        signatures_by_page[page_num].append(sig_data)

    # Process each page
    for page_idx in range(len(pdf_reader.pages)):
        page = pdf_reader.pages[page_idx]
        page_width = float(page.mediabox.width)
        page_height = float(page.mediabox.height)

        # Check if this page has signatures
        if page_idx in signatures_by_page:
            # Create overlay PDF for all signatures on this page
            signatures_overlay_buffer = io.BytesIO()
            c = canvas.Canvas(signatures_overlay_buffer, pagesize=(page_width, page_height))

            # Process each signature on this page
            for sig_data in signatures_by_page[page_idx]:
                signature_data = sig_data.get('signature')
                position = sig_data.get('position', {'x': 50, 'y': 90})
                size = sig_data.get('size', {'width': 150, 'height': 60})

                # Extract signature image from base64
                signature_img_data = signature_data.split(',', 1)[1] if ',' in signature_data else signature_data
                signature_img_bytes = base64.b64decode(signature_img_data)

                # Create signature image
                signature_img = Image.open(io.BytesIO(signature_img_bytes))
                if signature_img.mode != 'RGBA':
                    signature_img = signature_img.convert('RGBA')

                # Create buffer for this signature
                signature_buffer = io.BytesIO()
                signature_img.save(signature_buffer, format='PNG')
                signature_buffer.seek(0)

                # Calculate signature position (convert from percentage to points)
                x_pos = (position['x'] / 100.0) * page_width
                y_pos = (position['y'] / 100.0) * page_height

                # Get signature size in points
                sig_width = size['width']
                sig_height = size['height']

                # Calculate Y position from bottom (reportlab coordinates start from bottom)
                y_pos_adjusted = page_height - y_pos - sig_height / 2

                # Draw signature image on overlay
                c.drawImage(
                    ImageReader(signature_buffer),
                    x_pos - sig_width / 2,
                    y_pos_adjusted,
                    width=sig_width,
                    height=sig_height,
                    mask='auto',
                )

            c.save()

            # Overlay signatures on the page
            signatures_overlay_buffer.seek(0)
            signatures_overlay_pdf = PdfReader(signatures_overlay_buffer)
            page.merge_page(signatures_overlay_pdf.pages[0])

        # Add the page (modified or unmodified) to the new PDF
        pdf_writer.add_page(page)

    # Create a signed version of the PDF using a new opaque UUID filename
    signed_pdf_filename = generate_uuid_filename('.pdf')
    signed_pdf_path = safe_join(REPORTS_DIR, signed_pdf_filename)
    with open(signed_pdf_path, 'wb') as output_file:
        pdf_writer.write(output_file)

    # Update report metadata
    report['e_signed'] = True
    report['signatures_data'] = signatures_data
    report['signed_at'] = datetime.now().isoformat()
    report['pdf_path'] = signed_pdf_path  # Update path to signed version
    persist_reports_storage()

    # Audit: report signed
    try:
        audit_event(
            action='report_signed',
            user='anonymous',
            details={'report_id': report_id, 'num_signatures': len(signatures_data)}
        )
    except Exception:
        pass

    return jsonify({
        'message': 'Report signed successfully',
        'download_urls': {
            'docx': f'/api/reports/{report_id}/download/docx',
            'pdf': f'/api/reports/{report_id}/download/pdf'
        }
    })

@reports_bp.route('/reports/<report_id>/document', methods=['GET'])
def get_report_document(report_id):
    """Return the saved document content for a report as plain text.
    If no saved document exists yet, return 404 so the frontend can fall back to a template."""
    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break
    
    if not report:
        return error_response(404, 'Report not found')
    
    doc_path = report.get('document_txt_path')
    if doc_path and os.path.exists(doc_path):
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return Response(content, mimetype='text/plain')
        except Exception:
            # Let global handler format and log
            raise
    else:
        # Not found to trigger frontend fallback template
        return error_response(404, 'Document not found')

@reports_bp.route('/reports/<report_id>/save-document', methods=['POST'])
def save_report_document(report_id):
    """Save editor content for a report to a text file and update report metadata."""
    req = SaveDocumentRequest.model_validate(request.get_json(silent=True) or {})

    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break
    # If report does not exist, a stub will be created below so that saving still works

    # Ensure documents directory exists
    documents_dir = DOCUMENTS_DIR

    # Save content to file using safe path join. Reject traversal attempts
    try:
        doc_path = safe_join(documents_dir, f'{report_id}.txt')
    except ValueError:
        return error_response(400, 'Invalid path')
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(req.content if req.content is not None else '')

    # Update report metadata
    if not report:
        # If report doesn't exist, create a minimal stub so editing works even after restarts
        report = {
            'id': report_id,
            'template_id': 'joint_report',
            'template_name': TEMPLATES['joint_report']['name'],
            'form_data': {},
            'created_at': datetime.now().isoformat(),
            'e_signed': False
        }
        REPORTS_STORAGE.append(report)
    report['document_txt_path'] = doc_path
    report['document_last_saved_at'] = datetime.now().isoformat()
    persist_reports_storage()

    return jsonify({
        'message': 'Document saved successfully',
        'document_url': f'/api/reports/{report_id}/document'
    })

