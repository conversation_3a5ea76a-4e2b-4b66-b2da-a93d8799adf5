import os
import sys
import logging
import time
from dotenv import load_dotenv
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.models.user import db
from src.routes.user import user_bp
from src.routes.templates import templates_bp
from src.routes.reports import reports_bp
from src.routes.system import system_bp
from src.utils.errors import register_error_handlers

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
# Load environment variables from backend/.env if present
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
load_dotenv(os.path.join(BASE_DIR, '.env'))

# Structured logging configuration
log_level_name = os.getenv('LOG_LEVEL', 'INFO').upper()
log_level = getattr(logging, log_level_name, logging.INFO)
logging.basicConfig(
    level=log_level,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)],
    force=True,
)
logger = logging.getLogger(__name__)
logger.info("Configured logging with level %s", logging.getLevelName(log_level))

# Configure secret key with fallback default
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-change-me')

# Record application start time for health diagnostics
app.config['APP_START_TIME'] = time.time()

# Configure CORS for API routes only, with origins from env
allowed_origins_env = os.getenv('CORS_ALLOWED_ORIGINS')
if allowed_origins_env:
    allowed_origins = [origin.strip() for origin in allowed_origins_env.split(',') if origin.strip()]
else:
    # Default for local development
    allowed_origins = ['http://localhost:2000']

CORS(app, resources={r"/api/*": {"origins": allowed_origins}})
logger.info("CORS configured for /api/* with allowed origins: %s", allowed_origins)

app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(templates_bp, url_prefix='/api')
app.register_blueprint(reports_bp, url_prefix='/api')
app.register_blueprint(system_bp, url_prefix='/api')

# Register global error handlers (HTTP, validation, and unexpected)
register_error_handlers(app)

# uncomment if you need to use database
# Ensure database directory exists before configuring SQLAlchemy
database_dir = os.path.join(os.path.dirname(__file__), 'database')
os.makedirs(database_dir, exist_ok=True)
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(database_dir, 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
try:
    with app.app_context():
        db.create_all()
        logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Database initialization failed: {e}")
    # Continue without database for now

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=6000, debug=True)
