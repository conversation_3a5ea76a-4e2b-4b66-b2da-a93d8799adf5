import os
import uuid
from typing import Iterable

try:
    # Optional dependency; only used when handling uploads
    from werkzeug.utils import secure_filename as werkzeug_secure_filename
except Exception:  # pragma: no cover - optional in this project
    werkzeug_secure_filename = None  # type: ignore


def generate_uuid_filename(extension: str) -> str:
    """Generate a cryptographically-strong UUID filename with the given extension.

    The extension may be provided with or without a leading dot. The filename uses
    uuid.uuid4().hex to avoid hyphens and to keep names short and opaque.
    """
    ext = extension if extension.startswith('.') else f'.{extension}'
    return f"{uuid.uuid4().hex}{ext}"


def safe_join(base_directory: str, *path_segments: Iterable[str]) -> str:
    """Safely join path segments to a base directory.

    - Disallows absolute segments
    - Normalizes and resolves the final absolute path
    - Ensures the resolved path is contained within base_directory

    Raises ValueError if the resulting path escapes the base directory.
    """
    if not base_directory:
        raise ValueError("Base directory must be provided")

    base_abs = os.path.abspath(base_directory)
    segments: list[str] = []
    for seg in path_segments:
        if not seg:
            continue
        # Do not allow absolute paths in segments
        if os.path.isabs(str(seg)):
            raise ValueError("Absolute paths are not allowed in path segments")
        segments.append(str(seg))

    candidate = os.path.abspath(os.path.join(base_abs, *segments))

    # Ensure candidate stays within base_abs using commonpath (robust to .. and separators)
    try:
        common = os.path.commonpath([base_abs, candidate])
    except ValueError:
        # Different drives on Windows
        raise ValueError("Path resolution error: different drives or invalid path")

    if common != base_abs:
        raise ValueError("Attempted path traversal outside of base directory")

    return candidate


def secure_uploaded_filename(filename: str) -> str:
    """Sanitize an uploaded filename using Werkzeug's secure_filename.

    Falls back to generating a UUID name if Werkzeug is unavailable or the result is empty.
    """
    sanitized = None
    if werkzeug_secure_filename is not None:
        try:
            sanitized = werkzeug_secure_filename(filename or '')
        except Exception:
            sanitized = None

    if not sanitized:
        # Fall back to a safe opaque name without an extension
        sanitized = uuid.uuid4().hex

    return sanitized


