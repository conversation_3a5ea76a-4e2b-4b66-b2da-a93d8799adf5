import os
import json
import logging
import shutil
import time
from datetime import datetime, timezone

from flask import Blueprint, current_app, jsonify

from src.utils.reports_index import load_reports_index


system_bp = Blueprint('system', __name__)
logger = logging.getLogger(__name__)


# Resolve storage paths relative to src/ directory
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')


def _utc_now_iso8601_z() -> str:
    # Use Zulu time without microseconds to avoid leaking precision
    return datetime.now(timezone.utc).replace(microsecond=0).isoformat().replace('+00:00', 'Z')


def _get_uptime_seconds() -> float:
    app_start = current_app.config.get('APP_START_TIME', time.time())
    # Ensure non-negative
    return max(0.0, float(time.time() - app_start))


def _check_reports_index_access() -> bool:
    try:
        data = load_reports_index()
        # Consider success if JSON shape is list or dict
        return isinstance(data, (list, dict))
    except Exception as exc:  # pragma: no cover - defensive; helper already guards
        logger.error("healthcheck.reports_index_access_failed", extra={
            'event': 'healthcheck',
            'check': 'reports_index_access',
            'error': str(exc),
        })
        return False


def _check_disk_space_ok(threshold_bytes: int = 50 * 1024 * 1024) -> bool:
    try:
        os.makedirs(REPORTS_DIR, exist_ok=True)
        usage = shutil.disk_usage(REPORTS_DIR)
        return usage.free >= threshold_bytes
    except Exception as exc:
        logger.error("healthcheck.disk_space_check_failed", extra={
            'event': 'healthcheck',
            'check': 'disk_space_ok',
            'error': str(exc),
        })
        return False


@system_bp.route('/health', methods=['GET'])
def health():
    version = os.getenv('APP_VERSION', 'dev')

    checks = {
        'reports_index_access': _check_reports_index_access(),
        'disk_space_ok': _check_disk_space_ok(),
    }

    status = 'ok' if all(checks.values()) else 'degraded'

    if status != 'ok':
        # Log a single line for degraded health; avoid sensitive details
        logger.warning("healthcheck.degraded", extra={
            'event': 'healthcheck',
            'status': status,
            'failed_checks': [name for name, ok in checks.items() if not ok],
        })

    payload = {
        'status': status,
        'timestamp': _utc_now_iso8601_z(),
        'uptime_seconds': _get_uptime_seconds(),
        'version': version,
        'checks': checks,
    }
    return jsonify(payload)


if __name__ == '__main__':
    # Simple local diagnostic printout (does not start a server)
    sample = {
        'status': 'ok' if _check_reports_index_access() and _check_disk_space_ok() else 'degraded',
        'timestamp': _utc_now_iso8601_z(),
        'uptime_seconds': 0.0,
        'version': os.getenv('APP_VERSION', 'dev'),
        'checks': {
            'reports_index_access': _check_reports_index_access(),
            'disk_space_ok': _check_disk_space_ok(),
        },
    }
    print(json.dumps(sample, indent=2))


