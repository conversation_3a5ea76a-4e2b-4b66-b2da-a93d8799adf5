function I0(a,r){for(var u=0;u<r.length;u++){const o=r[u];if(typeof o!="string"&&!Array.isArray(o)){for(const s in o)if(s!=="default"&&!(s in a)){const f=Object.getOwnPropertyDescriptor(o,s);f&&Object.defineProperty(a,s,f.get?f:{enumerable:!0,get:()=>o[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function u(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(s){if(s.ep)return;s.ep=!0;const f=u(s);fetch(s.href,f)}})();function Vp(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Ic={exports:{}},zi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kh;function eb(){if(Kh)return zi;Kh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(o,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var p in s)p!=="key"&&(f[p]=s[p])}else f=s;return s=f.ref,{$$typeof:a,type:o,key:d,ref:s!==void 0?s:null,props:f}}return zi.Fragment=r,zi.jsx=u,zi.jsxs=u,zi}var Jh;function tb(){return Jh||(Jh=1,Ic.exports=eb()),Ic.exports}var x=tb(),es={exports:{}},ve={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h;function nb(){if($h)return ve;$h=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),w=Symbol.iterator;function E(R){return R===null||typeof R!="object"?null:(R=w&&R[w]||R["@@iterator"],typeof R=="function"?R:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},A=Object.assign,S={};function N(R,G,W){this.props=R,this.context=G,this.refs=S,this.updater=W||C}N.prototype.isReactComponent={},N.prototype.setState=function(R,G){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,G,"setState")},N.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function O(){}O.prototype=N.prototype;function M(R,G,W){this.props=R,this.context=G,this.refs=S,this.updater=W||C}var D=M.prototype=new O;D.constructor=M,A(D,N.prototype),D.isPureReactComponent=!0;var z=Array.isArray,q={H:null,A:null,T:null,S:null,V:null},P=Object.prototype.hasOwnProperty;function J(R,G,W,$,I,he){return W=he.ref,{$$typeof:a,type:R,key:G,ref:W!==void 0?W:null,props:he}}function K(R,G){return J(R.type,G,void 0,void 0,void 0,R.props)}function ee(R){return typeof R=="object"&&R!==null&&R.$$typeof===a}function fe(R){var G={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(W){return G[W]})}var pe=/\/+/g;function me(R,G){return typeof R=="object"&&R!==null&&R.key!=null?fe(""+R.key):G.toString(36)}function ge(){}function xe(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(ge,ge):(R.status="pending",R.then(function(G){R.status==="pending"&&(R.status="fulfilled",R.value=G)},function(G){R.status==="pending"&&(R.status="rejected",R.reason=G)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function ue(R,G,W,$,I){var he=typeof R;(he==="undefined"||he==="boolean")&&(R=null);var re=!1;if(R===null)re=!0;else switch(he){case"bigint":case"string":case"number":re=!0;break;case"object":switch(R.$$typeof){case a:case r:re=!0;break;case b:return re=R._init,ue(re(R._payload),G,W,$,I)}}if(re)return I=I(R),re=$===""?"."+me(R,0):$,z(I)?(W="",re!=null&&(W=re.replace(pe,"$&/")+"/"),ue(I,G,W,"",function(Me){return Me})):I!=null&&(ee(I)&&(I=K(I,W+(I.key==null||R&&R.key===I.key?"":(""+I.key).replace(pe,"$&/")+"/")+re)),G.push(I)),1;re=0;var F=$===""?".":$+":";if(z(R))for(var oe=0;oe<R.length;oe++)$=R[oe],he=F+me($,oe),re+=ue($,G,W,he,I);else if(oe=E(R),typeof oe=="function")for(R=oe.call(R),oe=0;!($=R.next()).done;)$=$.value,he=F+me($,oe++),re+=ue($,G,W,he,I);else if(he==="object"){if(typeof R.then=="function")return ue(xe(R),G,W,$,I);throw G=String(R),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return re}function L(R,G,W){if(R==null)return R;var $=[],I=0;return ue(R,$,"","",function(he){return G.call(W,he,I++)}),$}function Z(R){if(R._status===-1){var G=R._result;G=G(),G.then(function(W){(R._status===0||R._status===-1)&&(R._status=1,R._result=W)},function(W){(R._status===0||R._status===-1)&&(R._status=2,R._result=W)}),R._status===-1&&(R._status=0,R._result=G)}if(R._status===1)return R._result.default;throw R._result}var Y=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function ae(){}return ve.Children={map:L,forEach:function(R,G,W){L(R,function(){G.apply(this,arguments)},W)},count:function(R){var G=0;return L(R,function(){G++}),G},toArray:function(R){return L(R,function(G){return G})||[]},only:function(R){if(!ee(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},ve.Component=N,ve.Fragment=u,ve.Profiler=s,ve.PureComponent=M,ve.StrictMode=o,ve.Suspense=v,ve.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=q,ve.__COMPILER_RUNTIME={__proto__:null,c:function(R){return q.H.useMemoCache(R)}},ve.cache=function(R){return function(){return R.apply(null,arguments)}},ve.cloneElement=function(R,G,W){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var $=A({},R.props),I=R.key,he=void 0;if(G!=null)for(re in G.ref!==void 0&&(he=void 0),G.key!==void 0&&(I=""+G.key),G)!P.call(G,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&G.ref===void 0||($[re]=G[re]);var re=arguments.length-2;if(re===1)$.children=W;else if(1<re){for(var F=Array(re),oe=0;oe<re;oe++)F[oe]=arguments[oe+2];$.children=F}return J(R.type,I,void 0,void 0,he,$)},ve.createContext=function(R){return R={$$typeof:d,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:f,_context:R},R},ve.createElement=function(R,G,W){var $,I={},he=null;if(G!=null)for($ in G.key!==void 0&&(he=""+G.key),G)P.call(G,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(I[$]=G[$]);var re=arguments.length-2;if(re===1)I.children=W;else if(1<re){for(var F=Array(re),oe=0;oe<re;oe++)F[oe]=arguments[oe+2];I.children=F}if(R&&R.defaultProps)for($ in re=R.defaultProps,re)I[$]===void 0&&(I[$]=re[$]);return J(R,he,void 0,void 0,null,I)},ve.createRef=function(){return{current:null}},ve.forwardRef=function(R){return{$$typeof:p,render:R}},ve.isValidElement=ee,ve.lazy=function(R){return{$$typeof:b,_payload:{_status:-1,_result:R},_init:Z}},ve.memo=function(R,G){return{$$typeof:m,type:R,compare:G===void 0?null:G}},ve.startTransition=function(R){var G=q.T,W={};q.T=W;try{var $=R(),I=q.S;I!==null&&I(W,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(ae,Y)}catch(he){Y(he)}finally{q.T=G}},ve.unstable_useCacheRefresh=function(){return q.H.useCacheRefresh()},ve.use=function(R){return q.H.use(R)},ve.useActionState=function(R,G,W){return q.H.useActionState(R,G,W)},ve.useCallback=function(R,G){return q.H.useCallback(R,G)},ve.useContext=function(R){return q.H.useContext(R)},ve.useDebugValue=function(){},ve.useDeferredValue=function(R,G){return q.H.useDeferredValue(R,G)},ve.useEffect=function(R,G,W){var $=q.H;if(typeof W=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(R,G)},ve.useId=function(){return q.H.useId()},ve.useImperativeHandle=function(R,G,W){return q.H.useImperativeHandle(R,G,W)},ve.useInsertionEffect=function(R,G){return q.H.useInsertionEffect(R,G)},ve.useLayoutEffect=function(R,G){return q.H.useLayoutEffect(R,G)},ve.useMemo=function(R,G){return q.H.useMemo(R,G)},ve.useOptimistic=function(R,G){return q.H.useOptimistic(R,G)},ve.useReducer=function(R,G,W){return q.H.useReducer(R,G,W)},ve.useRef=function(R){return q.H.useRef(R)},ve.useState=function(R){return q.H.useState(R)},ve.useSyncExternalStore=function(R,G,W){return q.H.useSyncExternalStore(R,G,W)},ve.useTransition=function(){return q.H.useTransition()},ve.version="19.1.0",ve}var Ph;function js(){return Ph||(Ph=1,es.exports=nb()),es.exports}var y=js();const In=Vp(y),Gp=I0({__proto__:null,default:In},[y]);var ts={exports:{}},ji={},ns={exports:{}},ls={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wh;function lb(){return Wh||(Wh=1,function(a){function r(L,Z){var Y=L.length;L.push(Z);e:for(;0<Y;){var ae=Y-1>>>1,R=L[ae];if(0<s(R,Z))L[ae]=Z,L[Y]=R,Y=ae;else break e}}function u(L){return L.length===0?null:L[0]}function o(L){if(L.length===0)return null;var Z=L[0],Y=L.pop();if(Y!==Z){L[0]=Y;e:for(var ae=0,R=L.length,G=R>>>1;ae<G;){var W=2*(ae+1)-1,$=L[W],I=W+1,he=L[I];if(0>s($,Y))I<R&&0>s(he,$)?(L[ae]=he,L[I]=Y,ae=I):(L[ae]=$,L[W]=Y,ae=W);else if(I<R&&0>s(he,Y))L[ae]=he,L[I]=Y,ae=I;else break e}}return Z}function s(L,Z){var Y=L.sortIndex-Z.sortIndex;return Y!==0?Y:L.id-Z.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,p=d.now();a.unstable_now=function(){return d.now()-p}}var v=[],m=[],b=1,w=null,E=3,C=!1,A=!1,S=!1,N=!1,O=typeof setTimeout=="function"?setTimeout:null,M=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;function z(L){for(var Z=u(m);Z!==null;){if(Z.callback===null)o(m);else if(Z.startTime<=L)o(m),Z.sortIndex=Z.expirationTime,r(v,Z);else break;Z=u(m)}}function q(L){if(S=!1,z(L),!A)if(u(v)!==null)A=!0,P||(P=!0,me());else{var Z=u(m);Z!==null&&ue(q,Z.startTime-L)}}var P=!1,J=-1,K=5,ee=-1;function fe(){return N?!0:!(a.unstable_now()-ee<K)}function pe(){if(N=!1,P){var L=a.unstable_now();ee=L;var Z=!0;try{e:{A=!1,S&&(S=!1,M(J),J=-1),C=!0;var Y=E;try{t:{for(z(L),w=u(v);w!==null&&!(w.expirationTime>L&&fe());){var ae=w.callback;if(typeof ae=="function"){w.callback=null,E=w.priorityLevel;var R=ae(w.expirationTime<=L);if(L=a.unstable_now(),typeof R=="function"){w.callback=R,z(L),Z=!0;break t}w===u(v)&&o(v),z(L)}else o(v);w=u(v)}if(w!==null)Z=!0;else{var G=u(m);G!==null&&ue(q,G.startTime-L),Z=!1}}break e}finally{w=null,E=Y,C=!1}Z=void 0}}finally{Z?me():P=!1}}}var me;if(typeof D=="function")me=function(){D(pe)};else if(typeof MessageChannel<"u"){var ge=new MessageChannel,xe=ge.port2;ge.port1.onmessage=pe,me=function(){xe.postMessage(null)}}else me=function(){O(pe,0)};function ue(L,Z){J=O(function(){L(a.unstable_now())},Z)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(L){L.callback=null},a.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<L?Math.floor(1e3/L):5},a.unstable_getCurrentPriorityLevel=function(){return E},a.unstable_next=function(L){switch(E){case 1:case 2:case 3:var Z=3;break;default:Z=E}var Y=E;E=Z;try{return L()}finally{E=Y}},a.unstable_requestPaint=function(){N=!0},a.unstable_runWithPriority=function(L,Z){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var Y=E;E=L;try{return Z()}finally{E=Y}},a.unstable_scheduleCallback=function(L,Z,Y){var ae=a.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?ae+Y:ae):Y=ae,L){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=Y+R,L={id:b++,callback:Z,priorityLevel:L,startTime:Y,expirationTime:R,sortIndex:-1},Y>ae?(L.sortIndex=Y,r(m,L),u(v)===null&&L===u(m)&&(S?(M(J),J=-1):S=!0,ue(q,Y-ae))):(L.sortIndex=R,r(v,L),A||C||(A=!0,P||(P=!0,me()))),L},a.unstable_shouldYield=fe,a.unstable_wrapCallback=function(L){var Z=E;return function(){var Y=E;E=Z;try{return L.apply(this,arguments)}finally{E=Y}}}}(ls)),ls}var Fh;function ab(){return Fh||(Fh=1,ns.exports=lb()),ns.exports}var as={exports:{}},dt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ih;function ib(){if(Ih)return dt;Ih=1;var a=js();function r(v){var m="https://react.dev/errors/"+v;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)m+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+v+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(v,m,b){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:w==null?null:""+w,children:v,containerInfo:m,implementation:b}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(v,m){if(v==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return dt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,dt.createPortal=function(v,m){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(r(299));return f(v,m,null,b)},dt.flushSync=function(v){var m=d.T,b=o.p;try{if(d.T=null,o.p=2,v)return v()}finally{d.T=m,o.p=b,o.d.f()}},dt.preconnect=function(v,m){typeof v=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,o.d.C(v,m))},dt.prefetchDNS=function(v){typeof v=="string"&&o.d.D(v)},dt.preinit=function(v,m){if(typeof v=="string"&&m&&typeof m.as=="string"){var b=m.as,w=p(b,m.crossOrigin),E=typeof m.integrity=="string"?m.integrity:void 0,C=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;b==="style"?o.d.S(v,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:w,integrity:E,fetchPriority:C}):b==="script"&&o.d.X(v,{crossOrigin:w,integrity:E,fetchPriority:C,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},dt.preinitModule=function(v,m){if(typeof v=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var b=p(m.as,m.crossOrigin);o.d.M(v,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&o.d.M(v)},dt.preload=function(v,m){if(typeof v=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var b=m.as,w=p(b,m.crossOrigin);o.d.L(v,b,{crossOrigin:w,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},dt.preloadModule=function(v,m){if(typeof v=="string")if(m){var b=p(m.as,m.crossOrigin);o.d.m(v,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else o.d.m(v)},dt.requestFormReset=function(v){o.d.r(v)},dt.unstable_batchedUpdates=function(v,m){return v(m)},dt.useFormState=function(v,m,b){return d.H.useFormState(v,m,b)},dt.useFormStatus=function(){return d.H.useHostTransitionStatus()},dt.version="19.1.0",dt}var ep;function Xp(){if(ep)return as.exports;ep=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),as.exports=ib(),as.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tp;function rb(){if(tp)return ji;tp=1;var a=ab(),r=js(),u=Xp();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(f(e)!==e)throw Error(o(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var c=i.alternate;if(c===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===n)return p(i),e;if(c===l)return p(i),t;c=c.sibling}throw Error(o(188))}if(n.return!==l.return)n=i,l=c;else{for(var h=!1,g=i.child;g;){if(g===n){h=!0,n=i,l=c;break}if(g===l){h=!0,l=i,n=c;break}g=g.sibling}if(!h){for(g=c.child;g;){if(g===n){h=!0,n=c,l=i;break}if(g===l){h=!0,l=c,n=i;break}g=g.sibling}if(!h)throw Error(o(189))}}if(n.alternate!==l)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,w=Symbol.for("react.element"),E=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),A=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),M=Symbol.for("react.consumer"),D=Symbol.for("react.context"),z=Symbol.for("react.forward_ref"),q=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),J=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),ee=Symbol.for("react.activity"),fe=Symbol.for("react.memo_cache_sentinel"),pe=Symbol.iterator;function me(e){return e===null||typeof e!="object"?null:(e=pe&&e[pe]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Symbol.for("react.client.reference");function xe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ge?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case A:return"Fragment";case N:return"Profiler";case S:return"StrictMode";case q:return"Suspense";case P:return"SuspenseList";case ee:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case C:return"Portal";case D:return(e.displayName||"Context")+".Provider";case M:return(e._context.displayName||"Context")+".Consumer";case z:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case J:return t=e.displayName||null,t!==null?t:xe(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return xe(e(t))}catch{}}return null}var ue=Array.isArray,L=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y={pending:!1,data:null,method:null,action:null},ae=[],R=-1;function G(e){return{current:e}}function W(e){0>R||(e.current=ae[R],ae[R]=null,R--)}function $(e,t){R++,ae[R]=e.current,e.current=t}var I=G(null),he=G(null),re=G(null),F=G(null);function oe(e,t){switch($(re,t),$(he,e),$(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Sh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Sh(t),e=wh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}W(I),$(I,e)}function Me(){W(I),W(he),W(re)}function Re(e){e.memoizedState!==null&&$(F,e);var t=I.current,n=wh(t,e.type);t!==n&&($(he,e),$(I,n))}function we(e){he.current===e&&(W(I),W(he)),F.current===e&&(W(F),Ni._currentValue=Y)}var Ee=Object.prototype.hasOwnProperty,it=a.unstable_scheduleCallback,pt=a.unstable_cancelCallback,rl=a.unstable_shouldYield,ol=a.unstable_requestPaint,ct=a.unstable_now,Yo=a.unstable_getCurrentPriorityLevel,ul=a.unstable_ImmediatePriority,tf=a.unstable_UserBlockingPriority,Wi=a.unstable_NormalPriority,jg=a.unstable_LowPriority,nf=a.unstable_IdlePriority,Ug=a.log,Lg=a.unstable_setDisableYieldValue,La=null,wt=null;function Nn(e){if(typeof Ug=="function"&&Lg(e),wt&&typeof wt.setStrictMode=="function")try{wt.setStrictMode(La,e)}catch{}}var Et=Math.clz32?Math.clz32:kg,Hg=Math.log,Bg=Math.LN2;function kg(e){return e>>>=0,e===0?32:31-(Hg(e)/Bg|0)|0}var Fi=256,Ii=4194304;function cl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function er(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,c=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var g=l&134217727;return g!==0?(l=g&~c,l!==0?i=cl(l):(h&=g,h!==0?i=cl(h):n||(n=g&~e,n!==0&&(i=cl(n))))):(g=l&~c,g!==0?i=cl(g):h!==0?i=cl(h):n||(n=l&~e,n!==0&&(i=cl(n)))),i===0?0:t!==0&&t!==i&&(t&c)===0&&(c=i&-i,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:i}function Ha(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function qg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lf(){var e=Fi;return Fi<<=1,(Fi&4194048)===0&&(Fi=256),e}function af(){var e=Ii;return Ii<<=1,(Ii&62914560)===0&&(Ii=4194304),e}function Vo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ba(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Yg(e,t,n,l,i,c){var h=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var g=e.entanglements,T=e.expirationTimes,H=e.hiddenUpdates;for(n=h&~n;0<n;){var V=31-Et(n),Q=1<<V;g[V]=0,T[V]=-1;var B=H[V];if(B!==null)for(H[V]=null,V=0;V<B.length;V++){var k=B[V];k!==null&&(k.lane&=-536870913)}n&=~Q}l!==0&&rf(e,l,0),c!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=c&~(h&~t))}function rf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-Et(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function of(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-Et(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function Go(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Xo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function uf(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:Yh(e.type))}function Vg(e,t){var n=Z.p;try{return Z.p=e,t()}finally{Z.p=n}}var On=Math.random().toString(36).slice(2),st="__reactFiber$"+On,vt="__reactProps$"+On,Dl="__reactContainer$"+On,Qo="__reactEvents$"+On,Gg="__reactListeners$"+On,Xg="__reactHandles$"+On,cf="__reactResources$"+On,ka="__reactMarker$"+On;function Zo(e){delete e[st],delete e[vt],delete e[Qo],delete e[Gg],delete e[Xg]}function zl(e){var t=e[st];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Dl]||n[st]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Rh(e);e!==null;){if(n=e[st])return n;e=Rh(e)}return t}e=n,n=e.parentNode}return null}function jl(e){if(e=e[st]||e[Dl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function qa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function Ul(e){var t=e[cf];return t||(t=e[cf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Fe(e){e[ka]=!0}var sf=new Set,ff={};function sl(e,t){Ll(e,t),Ll(e+"Capture",t)}function Ll(e,t){for(ff[e]=t,e=0;e<t.length;e++)sf.add(t[e])}var Qg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),df={},mf={};function Zg(e){return Ee.call(mf,e)?!0:Ee.call(df,e)?!1:Qg.test(e)?mf[e]=!0:(df[e]=!0,!1)}function tr(e,t,n){if(Zg(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function nr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function rn(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var Ko,hf;function Hl(e){if(Ko===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ko=t&&t[1]||"",hf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ko+e+hf}var Jo=!1;function $o(e,t){if(!e||Jo)return"";Jo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(k){var B=k}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(k){B=k}e.call(Q.prototype)}}else{try{throw Error()}catch(k){B=k}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(k){if(k&&B&&typeof k.stack=="string")return[k.stack,B.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),h=c[0],g=c[1];if(h&&g){var T=h.split(`
`),H=g.split(`
`);for(i=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;i<H.length&&!H[i].includes("DetermineComponentFrameRoot");)i++;if(l===T.length||i===H.length)for(l=T.length-1,i=H.length-1;1<=l&&0<=i&&T[l]!==H[i];)i--;for(;1<=l&&0<=i;l--,i--)if(T[l]!==H[i]){if(l!==1||i!==1)do if(l--,i--,0>i||T[l]!==H[i]){var V=`
`+T[l].replace(" at new "," at ");return e.displayName&&V.includes("<anonymous>")&&(V=V.replace("<anonymous>",e.displayName)),V}while(1<=l&&0<=i);break}}}finally{Jo=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Hl(n):""}function Kg(e){switch(e.tag){case 26:case 27:case 5:return Hl(e.type);case 16:return Hl("Lazy");case 13:return Hl("Suspense");case 19:return Hl("SuspenseList");case 0:case 15:return $o(e.type,!1);case 11:return $o(e.type.render,!1);case 1:return $o(e.type,!0);case 31:return Hl("Activity");default:return""}}function pf(e){try{var t="";do t+=Kg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function zt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jg(e){var t=vf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){l=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function lr(e){e._valueTracker||(e._valueTracker=Jg(e))}function gf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=vf(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function ar(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var $g=/[\n"\\]/g;function jt(e){return e.replace($g,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Po(e,t,n,l,i,c,h,g){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+zt(t)):e.value!==""+zt(t)&&(e.value=""+zt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?Wo(e,h,zt(t)):n!=null?Wo(e,h,zt(n)):l!=null&&e.removeAttribute("value"),i==null&&c!=null&&(e.defaultChecked=!!c),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.name=""+zt(g):e.removeAttribute("name")}function yf(e,t,n,l,i,c,h,g){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+zt(n):"",t=t!=null?""+zt(t):n,g||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=g?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function Wo(e,t,n){t==="number"&&ar(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Bl(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function bf(e,t,n){if(t!=null&&(t=""+zt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+zt(n):""}function xf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(o(92));if(ue(l)){if(1<l.length)throw Error(o(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=zt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function kl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Pg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Sf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||Pg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function wf(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&Sf(e,i,l)}else for(var c in t)t.hasOwnProperty(c)&&Sf(e,c,t[c])}function Fo(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Wg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Fg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ir(e){return Fg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Io=null;function eu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ql=null,Yl=null;function Ef(e){var t=jl(e);if(t&&(e=t.stateNode)){var n=e[vt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Po(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+jt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[vt]||null;if(!i)throw Error(o(90));Po(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&gf(l)}break e;case"textarea":bf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Bl(e,!!n.multiple,t,!1)}}}var tu=!1;function Tf(e,t,n){if(tu)return e(t,n);tu=!0;try{var l=e(t);return l}finally{if(tu=!1,(ql!==null||Yl!==null)&&(Xr(),ql&&(t=ql,e=Yl,Yl=ql=null,Ef(t),e)))for(t=0;t<e.length;t++)Ef(e[t])}}function Ya(e,t){var n=e.stateNode;if(n===null)return null;var l=n[vt]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var on=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nu=!1;if(on)try{var Va={};Object.defineProperty(Va,"passive",{get:function(){nu=!0}}),window.addEventListener("test",Va,Va),window.removeEventListener("test",Va,Va)}catch{nu=!1}var Mn=null,lu=null,rr=null;function Af(){if(rr)return rr;var e,t=lu,n=t.length,l,i="value"in Mn?Mn.value:Mn.textContent,c=i.length;for(e=0;e<n&&t[e]===i[e];e++);var h=n-e;for(l=1;l<=h&&t[n-l]===i[c-l];l++);return rr=i.slice(e,1<l?1-l:void 0)}function or(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ur(){return!0}function Rf(){return!1}function gt(e){function t(n,l,i,c,h){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(n=e[g],this[g]=n?n(c):c[g]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ur:Rf,this.isPropagationStopped=Rf,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ur)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ur)},persist:function(){},isPersistent:ur}),t}var fl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cr=gt(fl),Ga=b({},fl,{view:0,detail:0}),Ig=gt(Ga),au,iu,Xa,sr=b({},Ga,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ou,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xa&&(Xa&&e.type==="mousemove"?(au=e.screenX-Xa.screenX,iu=e.screenY-Xa.screenY):iu=au=0,Xa=e),au)},movementY:function(e){return"movementY"in e?e.movementY:iu}}),Cf=gt(sr),ey=b({},sr,{dataTransfer:0}),ty=gt(ey),ny=b({},Ga,{relatedTarget:0}),ru=gt(ny),ly=b({},fl,{animationName:0,elapsedTime:0,pseudoElement:0}),ay=gt(ly),iy=b({},fl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ry=gt(iy),oy=b({},fl,{data:0}),Nf=gt(oy),uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},sy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=sy[e])?!!t[e]:!1}function ou(){return fy}var dy=b({},Ga,{key:function(e){if(e.key){var t=uy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=or(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?cy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ou,charCode:function(e){return e.type==="keypress"?or(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?or(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),my=gt(dy),hy=b({},sr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Of=gt(hy),py=b({},Ga,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ou}),vy=gt(py),gy=b({},fl,{propertyName:0,elapsedTime:0,pseudoElement:0}),yy=gt(gy),by=b({},sr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xy=gt(by),Sy=b({},fl,{newState:0,oldState:0}),wy=gt(Sy),Ey=[9,13,27,32],uu=on&&"CompositionEvent"in window,Qa=null;on&&"documentMode"in document&&(Qa=document.documentMode);var Ty=on&&"TextEvent"in window&&!Qa,Mf=on&&(!uu||Qa&&8<Qa&&11>=Qa),_f=" ",Df=!1;function zf(e,t){switch(e){case"keyup":return Ey.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vl=!1;function Ay(e,t){switch(e){case"compositionend":return jf(t);case"keypress":return t.which!==32?null:(Df=!0,_f);case"textInput":return e=t.data,e===_f&&Df?null:e;default:return null}}function Ry(e,t){if(Vl)return e==="compositionend"||!uu&&zf(e,t)?(e=Af(),rr=lu=Mn=null,Vl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mf&&t.locale!=="ko"?null:t.data;default:return null}}var Cy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Cy[e.type]:t==="textarea"}function Lf(e,t,n,l){ql?Yl?Yl.push(l):Yl=[l]:ql=l,t=Pr(t,"onChange"),0<t.length&&(n=new cr("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Za=null,Ka=null;function Ny(e){vh(e,0)}function fr(e){var t=qa(e);if(gf(t))return e}function Hf(e,t){if(e==="change")return t}var Bf=!1;if(on){var cu;if(on){var su="oninput"in document;if(!su){var kf=document.createElement("div");kf.setAttribute("oninput","return;"),su=typeof kf.oninput=="function"}cu=su}else cu=!1;Bf=cu&&(!document.documentMode||9<document.documentMode)}function qf(){Za&&(Za.detachEvent("onpropertychange",Yf),Ka=Za=null)}function Yf(e){if(e.propertyName==="value"&&fr(Ka)){var t=[];Lf(t,Ka,e,eu(e)),Tf(Ny,t)}}function Oy(e,t,n){e==="focusin"?(qf(),Za=t,Ka=n,Za.attachEvent("onpropertychange",Yf)):e==="focusout"&&qf()}function My(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return fr(Ka)}function _y(e,t){if(e==="click")return fr(t)}function Dy(e,t){if(e==="input"||e==="change")return fr(t)}function zy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:zy;function Ja(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Ee.call(t,i)||!Tt(e[i],t[i]))return!1}return!0}function Vf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Gf(e,t){var n=Vf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vf(n)}}function Xf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Xf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ar(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ar(e.document)}return t}function fu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var jy=on&&"documentMode"in document&&11>=document.documentMode,Gl=null,du=null,$a=null,mu=!1;function Zf(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;mu||Gl==null||Gl!==ar(l)||(l=Gl,"selectionStart"in l&&fu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),$a&&Ja($a,l)||($a=l,l=Pr(du,"onSelect"),0<l.length&&(t=new cr("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Gl)))}function dl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xl={animationend:dl("Animation","AnimationEnd"),animationiteration:dl("Animation","AnimationIteration"),animationstart:dl("Animation","AnimationStart"),transitionrun:dl("Transition","TransitionRun"),transitionstart:dl("Transition","TransitionStart"),transitioncancel:dl("Transition","TransitionCancel"),transitionend:dl("Transition","TransitionEnd")},hu={},Kf={};on&&(Kf=document.createElement("div").style,"AnimationEvent"in window||(delete Xl.animationend.animation,delete Xl.animationiteration.animation,delete Xl.animationstart.animation),"TransitionEvent"in window||delete Xl.transitionend.transition);function ml(e){if(hu[e])return hu[e];if(!Xl[e])return e;var t=Xl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Kf)return hu[e]=t[n];return e}var Jf=ml("animationend"),$f=ml("animationiteration"),Pf=ml("animationstart"),Uy=ml("transitionrun"),Ly=ml("transitionstart"),Hy=ml("transitioncancel"),Wf=ml("transitionend"),Ff=new Map,pu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");pu.push("scrollEnd");function Vt(e,t){Ff.set(e,t),sl(t,[e])}var If=new WeakMap;function Ut(e,t){if(typeof e=="object"&&e!==null){var n=If.get(e);return n!==void 0?n:(t={value:e,source:t,stack:pf(t)},If.set(e,t),t)}return{value:e,source:t,stack:pf(t)}}var Lt=[],Ql=0,vu=0;function dr(){for(var e=Ql,t=vu=Ql=0;t<e;){var n=Lt[t];Lt[t++]=null;var l=Lt[t];Lt[t++]=null;var i=Lt[t];Lt[t++]=null;var c=Lt[t];if(Lt[t++]=null,l!==null&&i!==null){var h=l.pending;h===null?i.next=i:(i.next=h.next,h.next=i),l.pending=i}c!==0&&ed(n,i,c)}}function mr(e,t,n,l){Lt[Ql++]=e,Lt[Ql++]=t,Lt[Ql++]=n,Lt[Ql++]=l,vu|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function gu(e,t,n,l){return mr(e,t,n,l),hr(e)}function Zl(e,t){return mr(e,null,null,t),hr(e)}function ed(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,c=e.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(i=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,i&&t!==null&&(i=31-Et(n),e=c.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),c):null}function hr(e){if(50<xi)throw xi=0,Ec=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Kl={};function By(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function At(e,t,n,l){return new By(e,t,n,l)}function yu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function un(e,t){var n=e.alternate;return n===null?(n=At(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function td(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function pr(e,t,n,l,i,c){var h=0;if(l=e,typeof e=="function")yu(e)&&(h=1);else if(typeof e=="string")h=q0(e,n,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ee:return e=At(31,n,t,i),e.elementType=ee,e.lanes=c,e;case A:return hl(n.children,i,c,t);case S:h=8,i|=24;break;case N:return e=At(12,n,t,i|2),e.elementType=N,e.lanes=c,e;case q:return e=At(13,n,t,i),e.elementType=q,e.lanes=c,e;case P:return e=At(19,n,t,i),e.elementType=P,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case O:case D:h=10;break e;case M:h=9;break e;case z:h=11;break e;case J:h=14;break e;case K:h=16,l=null;break e}h=29,n=Error(o(130,e===null?"null":typeof e,"")),l=null}return t=At(h,n,t,i),t.elementType=e,t.type=l,t.lanes=c,t}function hl(e,t,n,l){return e=At(7,e,l,t),e.lanes=n,e}function bu(e,t,n){return e=At(6,e,null,t),e.lanes=n,e}function xu(e,t,n){return t=At(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Jl=[],$l=0,vr=null,gr=0,Ht=[],Bt=0,pl=null,cn=1,sn="";function vl(e,t){Jl[$l++]=gr,Jl[$l++]=vr,vr=e,gr=t}function nd(e,t,n){Ht[Bt++]=cn,Ht[Bt++]=sn,Ht[Bt++]=pl,pl=e;var l=cn;e=sn;var i=32-Et(l)-1;l&=~(1<<i),n+=1;var c=32-Et(t)+i;if(30<c){var h=i-i%5;c=(l&(1<<h)-1).toString(32),l>>=h,i-=h,cn=1<<32-Et(t)+i|n<<i|l,sn=c+e}else cn=1<<c|n<<i|l,sn=e}function Su(e){e.return!==null&&(vl(e,1),nd(e,1,0))}function wu(e){for(;e===vr;)vr=Jl[--$l],Jl[$l]=null,gr=Jl[--$l],Jl[$l]=null;for(;e===pl;)pl=Ht[--Bt],Ht[Bt]=null,sn=Ht[--Bt],Ht[Bt]=null,cn=Ht[--Bt],Ht[Bt]=null}var ht=null,Ye=null,Oe=!1,gl=null,Jt=!1,Eu=Error(o(519));function yl(e){var t=Error(o(418,""));throw Fa(Ut(t,e)),Eu}function ld(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[st]=e,t[vt]=l,n){case"dialog":Ae("cancel",t),Ae("close",t);break;case"iframe":case"object":case"embed":Ae("load",t);break;case"video":case"audio":for(n=0;n<wi.length;n++)Ae(wi[n],t);break;case"source":Ae("error",t);break;case"img":case"image":case"link":Ae("error",t),Ae("load",t);break;case"details":Ae("toggle",t);break;case"input":Ae("invalid",t),yf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),lr(t);break;case"select":Ae("invalid",t);break;case"textarea":Ae("invalid",t),xf(t,l.value,l.defaultValue,l.children),lr(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||xh(t.textContent,n)?(l.popover!=null&&(Ae("beforetoggle",t),Ae("toggle",t)),l.onScroll!=null&&Ae("scroll",t),l.onScrollEnd!=null&&Ae("scrollend",t),l.onClick!=null&&(t.onclick=Wr),t=!0):t=!1,t||yl(e)}function ad(e){for(ht=e.return;ht;)switch(ht.tag){case 5:case 13:Jt=!1;return;case 27:case 3:Jt=!0;return;default:ht=ht.return}}function Pa(e){if(e!==ht)return!1;if(!Oe)return ad(e),Oe=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||kc(e.type,e.memoizedProps)),n=!n),n&&Ye&&yl(e),ad(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ye=Xt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ye=null}}else t===27?(t=Ye,Zn(e.type)?(e=Gc,Gc=null,Ye=e):Ye=t):Ye=ht?Xt(e.stateNode.nextSibling):null;return!0}function Wa(){Ye=ht=null,Oe=!1}function id(){var e=gl;return e!==null&&(xt===null?xt=e:xt.push.apply(xt,e),gl=null),e}function Fa(e){gl===null?gl=[e]:gl.push(e)}var Tu=G(null),bl=null,fn=null;function _n(e,t,n){$(Tu,t._currentValue),t._currentValue=n}function dn(e){e._currentValue=Tu.current,W(Tu)}function Au(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function Ru(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var c=i.dependencies;if(c!==null){var h=i.child;c=c.firstContext;e:for(;c!==null;){var g=c;c=i;for(var T=0;T<t.length;T++)if(g.context===t[T]){c.lanes|=n,g=c.alternate,g!==null&&(g.lanes|=n),Au(c.return,n,e),l||(h=null);break e}c=g.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(o(341));h.lanes|=n,c=h.alternate,c!==null&&(c.lanes|=n),Au(h,n,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function Ia(e,t,n,l){e=null;for(var i=t,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(o(387));if(h=h.memoizedProps,h!==null){var g=i.type;Tt(i.pendingProps.value,h.value)||(e!==null?e.push(g):e=[g])}}else if(i===F.current){if(h=i.alternate,h===null)throw Error(o(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ni):e=[Ni])}i=i.return}e!==null&&Ru(t,e,n,l),t.flags|=262144}function yr(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function xl(e){bl=e,fn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return rd(bl,e)}function br(e,t){return bl===null&&xl(e),rd(e,t)}function rd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},fn===null){if(e===null)throw Error(o(308));fn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else fn=fn.next=t;return n}var ky=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},qy=a.unstable_scheduleCallback,Yy=a.unstable_NormalPriority,$e={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Cu(){return{controller:new ky,data:new Map,refCount:0}}function ei(e){e.refCount--,e.refCount===0&&qy(Yy,function(){e.controller.abort()})}var ti=null,Nu=0,Pl=0,Wl=null;function Vy(e,t){if(ti===null){var n=ti=[];Nu=0,Pl=Mc(),Wl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Nu++,t.then(od,od),t}function od(){if(--Nu===0&&ti!==null){Wl!==null&&(Wl.status="fulfilled");var e=ti;ti=null,Pl=0,Wl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Gy(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var ud=L.S;L.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Vy(e,t),ud!==null&&ud(e,t)};var Sl=G(null);function Ou(){var e=Sl.current;return e!==null?e:He.pooledCache}function xr(e,t){t===null?$(Sl,Sl.current):$(Sl,t.pool)}function cd(){var e=Ou();return e===null?null:{parent:$e._currentValue,pool:e}}var ni=Error(o(460)),sd=Error(o(474)),Sr=Error(o(542)),Mu={then:function(){}};function fd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function wr(){}function dd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(wr,wr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hd(e),e;default:if(typeof t.status=="string")t.then(wr,wr);else{if(e=He,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hd(e),e}throw li=t,ni}}var li=null;function md(){if(li===null)throw Error(o(459));var e=li;return li=null,e}function hd(e){if(e===ni||e===Sr)throw Error(o(483))}var Dn=!1;function _u(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Du(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function zn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function jn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(_e&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=hr(e),ed(e,null,n),t}return mr(e,l,t,n),hr(e)}function ai(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,of(e,n)}}function zu(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?i=c=h:c=c.next=h,n=n.next}while(n!==null);c===null?i=c=t:c=c.next=t}else i=c=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ju=!1;function ii(){if(ju){var e=Wl;if(e!==null)throw e}}function ri(e,t,n,l){ju=!1;var i=e.updateQueue;Dn=!1;var c=i.firstBaseUpdate,h=i.lastBaseUpdate,g=i.shared.pending;if(g!==null){i.shared.pending=null;var T=g,H=T.next;T.next=null,h===null?c=H:h.next=H,h=T;var V=e.alternate;V!==null&&(V=V.updateQueue,g=V.lastBaseUpdate,g!==h&&(g===null?V.firstBaseUpdate=H:g.next=H,V.lastBaseUpdate=T))}if(c!==null){var Q=i.baseState;h=0,V=H=T=null,g=c;do{var B=g.lane&-536870913,k=B!==g.lane;if(k?(Ce&B)===B:(l&B)===B){B!==0&&B===Pl&&(ju=!0),V!==null&&(V=V.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});e:{var de=e,ce=g;B=t;var Ue=n;switch(ce.tag){case 1:if(de=ce.payload,typeof de=="function"){Q=de.call(Ue,Q,B);break e}Q=de;break e;case 3:de.flags=de.flags&-65537|128;case 0:if(de=ce.payload,B=typeof de=="function"?de.call(Ue,Q,B):de,B==null)break e;Q=b({},Q,B);break e;case 2:Dn=!0}}B=g.callback,B!==null&&(e.flags|=64,k&&(e.flags|=8192),k=i.callbacks,k===null?i.callbacks=[B]:k.push(B))}else k={lane:B,tag:g.tag,payload:g.payload,callback:g.callback,next:null},V===null?(H=V=k,T=Q):V=V.next=k,h|=B;if(g=g.next,g===null){if(g=i.shared.pending,g===null)break;k=g,g=k.next,k.next=null,i.lastBaseUpdate=k,i.shared.pending=null}}while(!0);V===null&&(T=Q),i.baseState=T,i.firstBaseUpdate=H,i.lastBaseUpdate=V,c===null&&(i.shared.lanes=0),Vn|=h,e.lanes=h,e.memoizedState=Q}}function pd(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function vd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)pd(n[e],t)}var Fl=G(null),Er=G(0);function gd(e,t){e=bn,$(Er,e),$(Fl,t),bn=e|t.baseLanes}function Uu(){$(Er,bn),$(Fl,Fl.current)}function Lu(){bn=Er.current,W(Fl),W(Er)}var Un=0,ye=null,ze=null,Ze=null,Tr=!1,Il=!1,wl=!1,Ar=0,oi=0,ea=null,Xy=0;function Xe(){throw Error(o(321))}function Hu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Tt(e[n],t[n]))return!1;return!0}function Bu(e,t,n,l,i,c){return Un=c,ye=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=e===null||e.memoizedState===null?em:tm,wl=!1,c=n(l,i),wl=!1,Il&&(c=bd(t,n,l,i)),yd(e),c}function yd(e){L.H=_r;var t=ze!==null&&ze.next!==null;if(Un=0,Ze=ze=ye=null,Tr=!1,oi=0,ea=null,t)throw Error(o(300));e===null||Ie||(e=e.dependencies,e!==null&&yr(e)&&(Ie=!0))}function bd(e,t,n,l){ye=e;var i=0;do{if(Il&&(ea=null),oi=0,Il=!1,25<=i)throw Error(o(301));if(i+=1,Ze=ze=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}L.H=Wy,c=t(n,l)}while(Il);return c}function Qy(){var e=L.H,t=e.useState()[0];return t=typeof t.then=="function"?ui(t):t,e=e.useState()[0],(ze!==null?ze.memoizedState:null)!==e&&(ye.flags|=1024),t}function ku(){var e=Ar!==0;return Ar=0,e}function qu(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Yu(e){if(Tr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Tr=!1}Un=0,Ze=ze=ye=null,Il=!1,oi=Ar=0,ea=null}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ze===null?ye.memoizedState=Ze=e:Ze=Ze.next=e,Ze}function Ke(){if(ze===null){var e=ye.alternate;e=e!==null?e.memoizedState:null}else e=ze.next;var t=Ze===null?ye.memoizedState:Ze.next;if(t!==null)Ze=t,ze=e;else{if(e===null)throw ye.alternate===null?Error(o(467)):Error(o(310));ze=e,e={memoizedState:ze.memoizedState,baseState:ze.baseState,baseQueue:ze.baseQueue,queue:ze.queue,next:null},Ze===null?ye.memoizedState=Ze=e:Ze=Ze.next=e}return Ze}function Vu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ui(e){var t=oi;return oi+=1,ea===null&&(ea=[]),e=dd(ea,e,t),t=ye,(Ze===null?t.memoizedState:Ze.next)===null&&(t=t.alternate,L.H=t===null||t.memoizedState===null?em:tm),e}function Rr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ui(e);if(e.$$typeof===D)return ft(e)}throw Error(o(438,String(e)))}function Gu(e){var t=null,n=ye.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=ye.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Vu(),ye.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=fe;return t.index++,n}function mn(e,t){return typeof t=="function"?t(e):t}function Cr(e){var t=Ke();return Xu(t,ze,e)}function Xu(e,t,n){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=n;var i=e.baseQueue,c=l.pending;if(c!==null){if(i!==null){var h=i.next;i.next=c.next,c.next=h}t.baseQueue=i=c,l.pending=null}if(c=e.baseState,i===null)e.memoizedState=c;else{t=i.next;var g=h=null,T=null,H=t,V=!1;do{var Q=H.lane&-536870913;if(Q!==H.lane?(Ce&Q)===Q:(Un&Q)===Q){var B=H.revertLane;if(B===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null}),Q===Pl&&(V=!0);else if((Un&B)===B){H=H.next,B===Pl&&(V=!0);continue}else Q={lane:0,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},T===null?(g=T=Q,h=c):T=T.next=Q,ye.lanes|=B,Vn|=B;Q=H.action,wl&&n(c,Q),c=H.hasEagerState?H.eagerState:n(c,Q)}else B={lane:Q,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},T===null?(g=T=B,h=c):T=T.next=B,ye.lanes|=Q,Vn|=Q;H=H.next}while(H!==null&&H!==t);if(T===null?h=c:T.next=g,!Tt(c,e.memoizedState)&&(Ie=!0,V&&(n=Wl,n!==null)))throw n;e.memoizedState=c,e.baseState=h,e.baseQueue=T,l.lastRenderedState=c}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Qu(e){var t=Ke(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,c=t.memoizedState;if(i!==null){n.pending=null;var h=i=i.next;do c=e(c,h.action),h=h.next;while(h!==i);Tt(c,t.memoizedState)||(Ie=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,l]}function xd(e,t,n){var l=ye,i=Ke(),c=Oe;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=t();var h=!Tt((ze||i).memoizedState,n);h&&(i.memoizedState=n,Ie=!0),i=i.queue;var g=Ed.bind(null,l,i,e);if(ci(2048,8,g,[e]),i.getSnapshot!==t||h||Ze!==null&&Ze.memoizedState.tag&1){if(l.flags|=2048,ta(9,Nr(),wd.bind(null,l,i,n,t),null),He===null)throw Error(o(349));c||(Un&124)!==0||Sd(l,t,n)}return n}function Sd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ye.updateQueue,t===null?(t=Vu(),ye.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function wd(e,t,n,l){t.value=n,t.getSnapshot=l,Td(t)&&Ad(e)}function Ed(e,t,n){return n(function(){Td(t)&&Ad(e)})}function Td(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Tt(e,n)}catch{return!0}}function Ad(e){var t=Zl(e,2);t!==null&&Mt(t,e,2)}function Zu(e){var t=yt();if(typeof e=="function"){var n=e;if(e=n(),wl){Nn(!0);try{n()}finally{Nn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:e},t}function Rd(e,t,n,l){return e.baseState=n,Xu(e,ze,typeof l=="function"?l:mn)}function Zy(e,t,n,l,i){if(Mr(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){c.listeners.push(h)}};L.T!==null?n(!0):c.isTransition=!1,l(c),n=t.pending,n===null?(c.next=t.pending=c,Cd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Cd(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var c=L.T,h={};L.T=h;try{var g=n(i,l),T=L.S;T!==null&&T(h,g),Nd(e,t,g)}catch(H){Ku(e,t,H)}finally{L.T=c}}else try{c=n(i,l),Nd(e,t,c)}catch(H){Ku(e,t,H)}}function Nd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Od(e,t,l)},function(l){return Ku(e,t,l)}):Od(e,t,n)}function Od(e,t,n){t.status="fulfilled",t.value=n,Md(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Cd(e,n)))}function Ku(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Md(t),t=t.next;while(t!==l)}e.action=null}function Md(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function _d(e,t){return t}function Dd(e,t){if(Oe){var n=He.formState;if(n!==null){e:{var l=ye;if(Oe){if(Ye){t:{for(var i=Ye,c=Jt;i.nodeType!==8;){if(!c){i=null;break t}if(i=Xt(i.nextSibling),i===null){i=null;break t}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Ye=Xt(i.nextSibling),l=i.data==="F!";break e}}yl(l)}l=!1}l&&(t=n[0])}}return n=yt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:_d,lastRenderedState:t},n.queue=l,n=Wd.bind(null,ye,l),l.dispatch=n,l=Zu(!1),c=Fu.bind(null,ye,!1,l.queue),l=yt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=Zy.bind(null,ye,i,c,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function zd(e){var t=Ke();return jd(t,ze,e)}function jd(e,t,n){if(t=Xu(e,t,_d)[0],e=Cr(mn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=ui(t)}catch(h){throw h===ni?Sr:h}else l=t;t=Ke();var i=t.queue,c=i.dispatch;return n!==t.memoizedState&&(ye.flags|=2048,ta(9,Nr(),Ky.bind(null,i,n),null)),[l,c,e]}function Ky(e,t){e.action=t}function Ud(e){var t=Ke(),n=ze;if(n!==null)return jd(t,n,e);Ke(),t=t.memoizedState,n=Ke();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ta(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=ye.updateQueue,t===null&&(t=Vu(),ye.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function Nr(){return{destroy:void 0,resource:void 0}}function Ld(){return Ke().memoizedState}function Or(e,t,n,l){var i=yt();l=l===void 0?null:l,ye.flags|=e,i.memoizedState=ta(1|t,Nr(),n,l)}function ci(e,t,n,l){var i=Ke();l=l===void 0?null:l;var c=i.memoizedState.inst;ze!==null&&l!==null&&Hu(l,ze.memoizedState.deps)?i.memoizedState=ta(t,c,n,l):(ye.flags|=e,i.memoizedState=ta(1|t,c,n,l))}function Hd(e,t){Or(8390656,8,e,t)}function Bd(e,t){ci(2048,8,e,t)}function kd(e,t){return ci(4,2,e,t)}function qd(e,t){return ci(4,4,e,t)}function Yd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vd(e,t,n){n=n!=null?n.concat([e]):null,ci(4,4,Yd.bind(null,t,e),n)}function Ju(){}function Gd(e,t){var n=Ke();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Hu(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function Xd(e,t){var n=Ke();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Hu(t,l[1]))return l[0];if(l=e(),wl){Nn(!0);try{e()}finally{Nn(!1)}}return n.memoizedState=[l,t],l}function $u(e,t,n){return n===void 0||(Un&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Km(),ye.lanes|=e,Vn|=e,n)}function Qd(e,t,n,l){return Tt(n,t)?n:Fl.current!==null?(e=$u(e,n,l),Tt(e,t)||(Ie=!0),e):(Un&42)===0?(Ie=!0,e.memoizedState=n):(e=Km(),ye.lanes|=e,Vn|=e,t)}function Zd(e,t,n,l,i){var c=Z.p;Z.p=c!==0&&8>c?c:8;var h=L.T,g={};L.T=g,Fu(e,!1,t,n);try{var T=i(),H=L.S;if(H!==null&&H(g,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var V=Gy(T,l);si(e,t,V,Ot(e))}else si(e,t,l,Ot(e))}catch(Q){si(e,t,{then:function(){},status:"rejected",reason:Q},Ot())}finally{Z.p=c,L.T=h}}function Jy(){}function Pu(e,t,n,l){if(e.tag!==5)throw Error(o(476));var i=Kd(e).queue;Zd(e,i,t,Y,n===null?Jy:function(){return Jd(e),n(l)})}function Kd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Y,baseState:Y,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:Y},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Jd(e){var t=Kd(e).next.queue;si(e,t,{},Ot())}function Wu(){return ft(Ni)}function $d(){return Ke().memoizedState}function Pd(){return Ke().memoizedState}function $y(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Ot();e=zn(n);var l=jn(t,e,n);l!==null&&(Mt(l,t,n),ai(l,t,n)),t={cache:Cu()},e.payload=t;return}t=t.return}}function Py(e,t,n){var l=Ot();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Mr(e)?Fd(t,n):(n=gu(e,t,n,l),n!==null&&(Mt(n,e,l),Id(n,t,l)))}function Wd(e,t,n){var l=Ot();si(e,t,n,l)}function si(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Mr(e))Fd(t,i);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,g=c(h,n);if(i.hasEagerState=!0,i.eagerState=g,Tt(g,h))return mr(e,t,i,0),He===null&&dr(),!1}catch{}finally{}if(n=gu(e,t,i,l),n!==null)return Mt(n,e,l),Id(n,t,l),!0}return!1}function Fu(e,t,n,l){if(l={lane:2,revertLane:Mc(),action:l,hasEagerState:!1,eagerState:null,next:null},Mr(e)){if(t)throw Error(o(479))}else t=gu(e,n,l,2),t!==null&&Mt(t,e,2)}function Mr(e){var t=e.alternate;return e===ye||t!==null&&t===ye}function Fd(e,t){Il=Tr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Id(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,of(e,n)}}var _r={readContext:ft,use:Rr,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useLayoutEffect:Xe,useInsertionEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useSyncExternalStore:Xe,useId:Xe,useHostTransitionStatus:Xe,useFormState:Xe,useActionState:Xe,useOptimistic:Xe,useMemoCache:Xe,useCacheRefresh:Xe},em={readContext:ft,use:Rr,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:Hd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Or(4194308,4,Yd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Or(4194308,4,e,t)},useInsertionEffect:function(e,t){Or(4,2,e,t)},useMemo:function(e,t){var n=yt();t=t===void 0?null:t;var l=e();if(wl){Nn(!0);try{e()}finally{Nn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=yt();if(n!==void 0){var i=n(t);if(wl){Nn(!0);try{n(t)}finally{Nn(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=Py.bind(null,ye,e),[l.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:function(e){e=Zu(e);var t=e.queue,n=Wd.bind(null,ye,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ju,useDeferredValue:function(e,t){var n=yt();return $u(n,e,t)},useTransition:function(){var e=Zu(!1);return e=Zd.bind(null,ye,e.queue,!0,!1),yt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=ye,i=yt();if(Oe){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),He===null)throw Error(o(349));(Ce&124)!==0||Sd(l,t,n)}i.memoizedState=n;var c={value:n,getSnapshot:t};return i.queue=c,Hd(Ed.bind(null,l,c,e),[e]),l.flags|=2048,ta(9,Nr(),wd.bind(null,l,c,n,t),null),n},useId:function(){var e=yt(),t=He.identifierPrefix;if(Oe){var n=sn,l=cn;n=(l&~(1<<32-Et(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=Ar++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Xy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Wu,useFormState:Dd,useActionState:Dd,useOptimistic:function(e){var t=yt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Fu.bind(null,ye,!0,n),n.dispatch=t,[e,t]},useMemoCache:Gu,useCacheRefresh:function(){return yt().memoizedState=$y.bind(null,ye)}},tm={readContext:ft,use:Rr,useCallback:Gd,useContext:ft,useEffect:Bd,useImperativeHandle:Vd,useInsertionEffect:kd,useLayoutEffect:qd,useMemo:Xd,useReducer:Cr,useRef:Ld,useState:function(){return Cr(mn)},useDebugValue:Ju,useDeferredValue:function(e,t){var n=Ke();return Qd(n,ze.memoizedState,e,t)},useTransition:function(){var e=Cr(mn)[0],t=Ke().memoizedState;return[typeof e=="boolean"?e:ui(e),t]},useSyncExternalStore:xd,useId:$d,useHostTransitionStatus:Wu,useFormState:zd,useActionState:zd,useOptimistic:function(e,t){var n=Ke();return Rd(n,ze,e,t)},useMemoCache:Gu,useCacheRefresh:Pd},Wy={readContext:ft,use:Rr,useCallback:Gd,useContext:ft,useEffect:Bd,useImperativeHandle:Vd,useInsertionEffect:kd,useLayoutEffect:qd,useMemo:Xd,useReducer:Qu,useRef:Ld,useState:function(){return Qu(mn)},useDebugValue:Ju,useDeferredValue:function(e,t){var n=Ke();return ze===null?$u(n,e,t):Qd(n,ze.memoizedState,e,t)},useTransition:function(){var e=Qu(mn)[0],t=Ke().memoizedState;return[typeof e=="boolean"?e:ui(e),t]},useSyncExternalStore:xd,useId:$d,useHostTransitionStatus:Wu,useFormState:Ud,useActionState:Ud,useOptimistic:function(e,t){var n=Ke();return ze!==null?Rd(n,ze,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Gu,useCacheRefresh:Pd},na=null,fi=0;function Dr(e){var t=fi;return fi+=1,na===null&&(na=[]),dd(na,e,t)}function di(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function zr(e,t){throw t.$$typeof===w?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function nm(e){var t=e._init;return t(e._payload)}function lm(e){function t(j,_){if(e){var U=j.deletions;U===null?(j.deletions=[_],j.flags|=16):U.push(_)}}function n(j,_){if(!e)return null;for(;_!==null;)t(j,_),_=_.sibling;return null}function l(j){for(var _=new Map;j!==null;)j.key!==null?_.set(j.key,j):_.set(j.index,j),j=j.sibling;return _}function i(j,_){return j=un(j,_),j.index=0,j.sibling=null,j}function c(j,_,U){return j.index=U,e?(U=j.alternate,U!==null?(U=U.index,U<_?(j.flags|=67108866,_):U):(j.flags|=67108866,_)):(j.flags|=1048576,_)}function h(j){return e&&j.alternate===null&&(j.flags|=67108866),j}function g(j,_,U,X){return _===null||_.tag!==6?(_=bu(U,j.mode,X),_.return=j,_):(_=i(_,U),_.return=j,_)}function T(j,_,U,X){var te=U.type;return te===A?V(j,_,U.props.children,X,U.key):_!==null&&(_.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===K&&nm(te)===_.type)?(_=i(_,U.props),di(_,U),_.return=j,_):(_=pr(U.type,U.key,U.props,null,j.mode,X),di(_,U),_.return=j,_)}function H(j,_,U,X){return _===null||_.tag!==4||_.stateNode.containerInfo!==U.containerInfo||_.stateNode.implementation!==U.implementation?(_=xu(U,j.mode,X),_.return=j,_):(_=i(_,U.children||[]),_.return=j,_)}function V(j,_,U,X,te){return _===null||_.tag!==7?(_=hl(U,j.mode,X,te),_.return=j,_):(_=i(_,U),_.return=j,_)}function Q(j,_,U){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=bu(""+_,j.mode,U),_.return=j,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case E:return U=pr(_.type,_.key,_.props,null,j.mode,U),di(U,_),U.return=j,U;case C:return _=xu(_,j.mode,U),_.return=j,_;case K:var X=_._init;return _=X(_._payload),Q(j,_,U)}if(ue(_)||me(_))return _=hl(_,j.mode,U,null),_.return=j,_;if(typeof _.then=="function")return Q(j,Dr(_),U);if(_.$$typeof===D)return Q(j,br(j,_),U);zr(j,_)}return null}function B(j,_,U,X){var te=_!==null?_.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return te!==null?null:g(j,_,""+U,X);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case E:return U.key===te?T(j,_,U,X):null;case C:return U.key===te?H(j,_,U,X):null;case K:return te=U._init,U=te(U._payload),B(j,_,U,X)}if(ue(U)||me(U))return te!==null?null:V(j,_,U,X,null);if(typeof U.then=="function")return B(j,_,Dr(U),X);if(U.$$typeof===D)return B(j,_,br(j,U),X);zr(j,U)}return null}function k(j,_,U,X,te){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return j=j.get(U)||null,g(_,j,""+X,te);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case E:return j=j.get(X.key===null?U:X.key)||null,T(_,j,X,te);case C:return j=j.get(X.key===null?U:X.key)||null,H(_,j,X,te);case K:var Se=X._init;return X=Se(X._payload),k(j,_,U,X,te)}if(ue(X)||me(X))return j=j.get(U)||null,V(_,j,X,te,null);if(typeof X.then=="function")return k(j,_,U,Dr(X),te);if(X.$$typeof===D)return k(j,_,U,br(_,X),te);zr(_,X)}return null}function de(j,_,U,X){for(var te=null,Se=null,ie=_,se=_=0,tt=null;ie!==null&&se<U.length;se++){ie.index>se?(tt=ie,ie=null):tt=ie.sibling;var Ne=B(j,ie,U[se],X);if(Ne===null){ie===null&&(ie=tt);break}e&&ie&&Ne.alternate===null&&t(j,ie),_=c(Ne,_,se),Se===null?te=Ne:Se.sibling=Ne,Se=Ne,ie=tt}if(se===U.length)return n(j,ie),Oe&&vl(j,se),te;if(ie===null){for(;se<U.length;se++)ie=Q(j,U[se],X),ie!==null&&(_=c(ie,_,se),Se===null?te=ie:Se.sibling=ie,Se=ie);return Oe&&vl(j,se),te}for(ie=l(ie);se<U.length;se++)tt=k(ie,j,se,U[se],X),tt!==null&&(e&&tt.alternate!==null&&ie.delete(tt.key===null?se:tt.key),_=c(tt,_,se),Se===null?te=tt:Se.sibling=tt,Se=tt);return e&&ie.forEach(function(Wn){return t(j,Wn)}),Oe&&vl(j,se),te}function ce(j,_,U,X){if(U==null)throw Error(o(151));for(var te=null,Se=null,ie=_,se=_=0,tt=null,Ne=U.next();ie!==null&&!Ne.done;se++,Ne=U.next()){ie.index>se?(tt=ie,ie=null):tt=ie.sibling;var Wn=B(j,ie,Ne.value,X);if(Wn===null){ie===null&&(ie=tt);break}e&&ie&&Wn.alternate===null&&t(j,ie),_=c(Wn,_,se),Se===null?te=Wn:Se.sibling=Wn,Se=Wn,ie=tt}if(Ne.done)return n(j,ie),Oe&&vl(j,se),te;if(ie===null){for(;!Ne.done;se++,Ne=U.next())Ne=Q(j,Ne.value,X),Ne!==null&&(_=c(Ne,_,se),Se===null?te=Ne:Se.sibling=Ne,Se=Ne);return Oe&&vl(j,se),te}for(ie=l(ie);!Ne.done;se++,Ne=U.next())Ne=k(ie,j,se,Ne.value,X),Ne!==null&&(e&&Ne.alternate!==null&&ie.delete(Ne.key===null?se:Ne.key),_=c(Ne,_,se),Se===null?te=Ne:Se.sibling=Ne,Se=Ne);return e&&ie.forEach(function(F0){return t(j,F0)}),Oe&&vl(j,se),te}function Ue(j,_,U,X){if(typeof U=="object"&&U!==null&&U.type===A&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case E:e:{for(var te=U.key;_!==null;){if(_.key===te){if(te=U.type,te===A){if(_.tag===7){n(j,_.sibling),X=i(_,U.props.children),X.return=j,j=X;break e}}else if(_.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===K&&nm(te)===_.type){n(j,_.sibling),X=i(_,U.props),di(X,U),X.return=j,j=X;break e}n(j,_);break}else t(j,_);_=_.sibling}U.type===A?(X=hl(U.props.children,j.mode,X,U.key),X.return=j,j=X):(X=pr(U.type,U.key,U.props,null,j.mode,X),di(X,U),X.return=j,j=X)}return h(j);case C:e:{for(te=U.key;_!==null;){if(_.key===te)if(_.tag===4&&_.stateNode.containerInfo===U.containerInfo&&_.stateNode.implementation===U.implementation){n(j,_.sibling),X=i(_,U.children||[]),X.return=j,j=X;break e}else{n(j,_);break}else t(j,_);_=_.sibling}X=xu(U,j.mode,X),X.return=j,j=X}return h(j);case K:return te=U._init,U=te(U._payload),Ue(j,_,U,X)}if(ue(U))return de(j,_,U,X);if(me(U)){if(te=me(U),typeof te!="function")throw Error(o(150));return U=te.call(U),ce(j,_,U,X)}if(typeof U.then=="function")return Ue(j,_,Dr(U),X);if(U.$$typeof===D)return Ue(j,_,br(j,U),X);zr(j,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,_!==null&&_.tag===6?(n(j,_.sibling),X=i(_,U),X.return=j,j=X):(n(j,_),X=bu(U,j.mode,X),X.return=j,j=X),h(j)):n(j,_)}return function(j,_,U,X){try{fi=0;var te=Ue(j,_,U,X);return na=null,te}catch(ie){if(ie===ni||ie===Sr)throw ie;var Se=At(29,ie,null,j.mode);return Se.lanes=X,Se.return=j,Se}finally{}}}var la=lm(!0),am=lm(!1),kt=G(null),$t=null;function Ln(e){var t=e.alternate;$(Pe,Pe.current&1),$(kt,e),$t===null&&(t===null||Fl.current!==null||t.memoizedState!==null)&&($t=e)}function im(e){if(e.tag===22){if($(Pe,Pe.current),$(kt,e),$t===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&($t=e)}}else Hn()}function Hn(){$(Pe,Pe.current),$(kt,kt.current)}function hn(e){W(kt),$t===e&&($t=null),W(Pe)}var Pe=G(0);function jr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Vc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Iu(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:b({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ec={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Ot(),i=zn(l);i.payload=t,n!=null&&(i.callback=n),t=jn(e,i,l),t!==null&&(Mt(t,e,l),ai(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Ot(),i=zn(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=jn(e,i,l),t!==null&&(Mt(t,e,l),ai(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ot(),l=zn(n);l.tag=2,t!=null&&(l.callback=t),t=jn(e,l,n),t!==null&&(Mt(t,e,n),ai(t,e,n))}};function rm(e,t,n,l,i,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,h):t.prototype&&t.prototype.isPureReactComponent?!Ja(n,l)||!Ja(i,c):!0}function om(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&ec.enqueueReplaceState(t,t.state,null)}function El(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=b({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Ur=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function um(e){Ur(e)}function cm(e){console.error(e)}function sm(e){Ur(e)}function Lr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function fm(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function tc(e,t,n){return n=zn(n),n.tag=3,n.payload={element:null},n.callback=function(){Lr(e,t)},n}function dm(e){return e=zn(e),e.tag=3,e}function mm(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var c=l.value;e.payload=function(){return i(c)},e.callback=function(){fm(t,n,l)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){fm(t,n,l),typeof i!="function"&&(Gn===null?Gn=new Set([this]):Gn.add(this));var g=l.stack;this.componentDidCatch(l.value,{componentStack:g!==null?g:""})})}function Fy(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&Ia(t,n,i,!0),n=kt.current,n!==null){switch(n.tag){case 13:return $t===null?Ac():n.alternate===null&&Ve===0&&(Ve=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Mu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Cc(e,l,i)),!1;case 22:return n.flags|=65536,l===Mu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Cc(e,l,i)),!1}throw Error(o(435,n.tag))}return Cc(e,l,i),Ac(),!1}if(Oe)return t=kt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==Eu&&(e=Error(o(422),{cause:l}),Fa(Ut(e,n)))):(l!==Eu&&(t=Error(o(423),{cause:l}),Fa(Ut(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Ut(l,n),i=tc(e.stateNode,l,i),zu(e,i),Ve!==4&&(Ve=2)),!1;var c=Error(o(520),{cause:l});if(c=Ut(c,n),bi===null?bi=[c]:bi.push(c),Ve!==4&&(Ve=2),t===null)return!0;l=Ut(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=tc(n.stateNode,l,e),zu(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Gn===null||!Gn.has(c))))return n.flags|=65536,i&=-i,n.lanes|=i,i=dm(i),mm(i,e,n,l),zu(n,i),!1}n=n.return}while(n!==null);return!1}var hm=Error(o(461)),Ie=!1;function rt(e,t,n,l){t.child=e===null?am(t,null,n,l):la(t,e.child,n,l)}function pm(e,t,n,l,i){n=n.render;var c=t.ref;if("ref"in l){var h={};for(var g in l)g!=="ref"&&(h[g]=l[g])}else h=l;return xl(t),l=Bu(e,t,n,h,c,i),g=ku(),e!==null&&!Ie?(qu(e,t,i),pn(e,t,i)):(Oe&&g&&Su(t),t.flags|=1,rt(e,t,l,i),t.child)}function vm(e,t,n,l,i){if(e===null){var c=n.type;return typeof c=="function"&&!yu(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,gm(e,t,c,l,i)):(e=pr(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!cc(e,i)){var h=c.memoizedProps;if(n=n.compare,n=n!==null?n:Ja,n(h,l)&&e.ref===t.ref)return pn(e,t,i)}return t.flags|=1,e=un(c,l),e.ref=t.ref,e.return=t,t.child=e}function gm(e,t,n,l,i){if(e!==null){var c=e.memoizedProps;if(Ja(c,l)&&e.ref===t.ref)if(Ie=!1,t.pendingProps=l=c,cc(e,i))(e.flags&131072)!==0&&(Ie=!0);else return t.lanes=e.lanes,pn(e,t,i)}return nc(e,t,n,l,i)}function ym(e,t,n){var l=t.pendingProps,i=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,e!==null){for(i=t.child=e.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return bm(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&xr(t,c!==null?c.cachePool:null),c!==null?gd(t,c):Uu(),im(t);else return t.lanes=t.childLanes=536870912,bm(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(xr(t,c.cachePool),gd(t,c),Hn(),t.memoizedState=null):(e!==null&&xr(t,null),Uu(),Hn());return rt(e,t,i,n),t.child}function bm(e,t,n,l){var i=Ou();return i=i===null?null:{parent:$e._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&xr(t,null),Uu(),im(t),e!==null&&Ia(e,t,l,!0),null}function Hr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function nc(e,t,n,l,i){return xl(t),n=Bu(e,t,n,l,void 0,i),l=ku(),e!==null&&!Ie?(qu(e,t,i),pn(e,t,i)):(Oe&&l&&Su(t),t.flags|=1,rt(e,t,n,i),t.child)}function xm(e,t,n,l,i,c){return xl(t),t.updateQueue=null,n=bd(t,l,n,i),yd(e),l=ku(),e!==null&&!Ie?(qu(e,t,c),pn(e,t,c)):(Oe&&l&&Su(t),t.flags|=1,rt(e,t,n,c),t.child)}function Sm(e,t,n,l,i){if(xl(t),t.stateNode===null){var c=Kl,h=n.contextType;typeof h=="object"&&h!==null&&(c=ft(h)),c=new n(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=ec,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},_u(t),h=n.contextType,c.context=typeof h=="object"&&h!==null?ft(h):Kl,c.state=t.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(Iu(t,n,h,l),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(h=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),h!==c.state&&ec.enqueueReplaceState(c,c.state,null),ri(t,l,c,i),ii(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var g=t.memoizedProps,T=El(n,g);c.props=T;var H=c.context,V=n.contextType;h=Kl,typeof V=="object"&&V!==null&&(h=ft(V));var Q=n.getDerivedStateFromProps;V=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",g=t.pendingProps!==g,V||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(g||H!==h)&&om(t,c,l,h),Dn=!1;var B=t.memoizedState;c.state=B,ri(t,l,c,i),ii(),H=t.memoizedState,g||B!==H||Dn?(typeof Q=="function"&&(Iu(t,n,Q,l),H=t.memoizedState),(T=Dn||rm(t,n,T,l,B,H,h))?(V||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=H),c.props=l,c.state=H,c.context=h,l=T):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Du(e,t),h=t.memoizedProps,V=El(n,h),c.props=V,Q=t.pendingProps,B=c.context,H=n.contextType,T=Kl,typeof H=="object"&&H!==null&&(T=ft(H)),g=n.getDerivedStateFromProps,(H=typeof g=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h!==Q||B!==T)&&om(t,c,l,T),Dn=!1,B=t.memoizedState,c.state=B,ri(t,l,c,i),ii();var k=t.memoizedState;h!==Q||B!==k||Dn||e!==null&&e.dependencies!==null&&yr(e.dependencies)?(typeof g=="function"&&(Iu(t,n,g,l),k=t.memoizedState),(V=Dn||rm(t,n,V,l,B,k,T)||e!==null&&e.dependencies!==null&&yr(e.dependencies))?(H||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,k,T),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,k,T)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=k),c.props=l,c.state=k,c.context=T,l=V):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,Hr(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=la(t,e.child,null,i),t.child=la(t,null,n,i)):rt(e,t,n,i),t.memoizedState=c.state,e=t.child):e=pn(e,t,i),e}function wm(e,t,n,l){return Wa(),t.flags|=256,rt(e,t,n,l),t.child}var lc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ac(e){return{baseLanes:e,cachePool:cd()}}function ic(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=qt),e}function Em(e,t,n){var l=t.pendingProps,i=!1,c=(t.flags&128)!==0,h;if((h=c)||(h=e!==null&&e.memoizedState===null?!1:(Pe.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Oe){if(i?Ln(t):Hn(),Oe){var g=Ye,T;if(T=g){e:{for(T=g,g=Jt;T.nodeType!==8;){if(!g){g=null;break e}if(T=Xt(T.nextSibling),T===null){g=null;break e}}g=T}g!==null?(t.memoizedState={dehydrated:g,treeContext:pl!==null?{id:cn,overflow:sn}:null,retryLane:536870912,hydrationErrors:null},T=At(18,null,null,0),T.stateNode=g,T.return=t,t.child=T,ht=t,Ye=null,T=!0):T=!1}T||yl(t)}if(g=t.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return Vc(g)?t.lanes=32:t.lanes=536870912,null;hn(t)}return g=l.children,l=l.fallback,i?(Hn(),i=t.mode,g=Br({mode:"hidden",children:g},i),l=hl(l,i,n,null),g.return=t,l.return=t,g.sibling=l,t.child=g,i=t.child,i.memoizedState=ac(n),i.childLanes=ic(e,h,n),t.memoizedState=lc,l):(Ln(t),rc(t,g))}if(T=e.memoizedState,T!==null&&(g=T.dehydrated,g!==null)){if(c)t.flags&256?(Ln(t),t.flags&=-257,t=oc(e,t,n)):t.memoizedState!==null?(Hn(),t.child=e.child,t.flags|=128,t=null):(Hn(),i=l.fallback,g=t.mode,l=Br({mode:"visible",children:l.children},g),i=hl(i,g,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,la(t,e.child,null,n),l=t.child,l.memoizedState=ac(n),l.childLanes=ic(e,h,n),t.memoizedState=lc,t=i);else if(Ln(t),Vc(g)){if(h=g.nextSibling&&g.nextSibling.dataset,h)var H=h.dgst;h=H,l=Error(o(419)),l.stack="",l.digest=h,Fa({value:l,source:null,stack:null}),t=oc(e,t,n)}else if(Ie||Ia(e,t,n,!1),h=(n&e.childLanes)!==0,Ie||h){if(h=He,h!==null&&(l=n&-n,l=(l&42)!==0?1:Go(l),l=(l&(h.suspendedLanes|n))!==0?0:l,l!==0&&l!==T.retryLane))throw T.retryLane=l,Zl(e,l),Mt(h,e,l),hm;g.data==="$?"||Ac(),t=oc(e,t,n)}else g.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=T.treeContext,Ye=Xt(g.nextSibling),ht=t,Oe=!0,gl=null,Jt=!1,e!==null&&(Ht[Bt++]=cn,Ht[Bt++]=sn,Ht[Bt++]=pl,cn=e.id,sn=e.overflow,pl=t),t=rc(t,l.children),t.flags|=4096);return t}return i?(Hn(),i=l.fallback,g=t.mode,T=e.child,H=T.sibling,l=un(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&65011712,H!==null?i=un(H,i):(i=hl(i,g,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,g=e.child.memoizedState,g===null?g=ac(n):(T=g.cachePool,T!==null?(H=$e._currentValue,T=T.parent!==H?{parent:H,pool:H}:T):T=cd(),g={baseLanes:g.baseLanes|n,cachePool:T}),i.memoizedState=g,i.childLanes=ic(e,h,n),t.memoizedState=lc,l):(Ln(t),n=e.child,e=n.sibling,n=un(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=n,t.memoizedState=null,n)}function rc(e,t){return t=Br({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Br(e,t){return e=At(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function oc(e,t,n){return la(t,e.child,null,n),e=rc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Tm(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Au(e.return,t,n)}function uc(e,t,n,l,i){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=i)}function Am(e,t,n){var l=t.pendingProps,i=l.revealOrder,c=l.tail;if(rt(e,t,l.children,n),l=Pe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Tm(e,n,t);else if(e.tag===19)Tm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch($(Pe,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&jr(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),uc(t,!1,i,n,c);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&jr(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}uc(t,!0,n,null,c);break;case"together":uc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function pn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Ia(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=un(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=un(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function cc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&yr(e)))}function Iy(e,t,n){switch(t.tag){case 3:oe(t,t.stateNode.containerInfo),_n(t,$e,e.memoizedState.cache),Wa();break;case 27:case 5:Re(t);break;case 4:oe(t,t.stateNode.containerInfo);break;case 10:_n(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Ln(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Em(e,t,n):(Ln(t),e=pn(e,t,n),e!==null?e.sibling:null);Ln(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(Ia(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return Am(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(Pe,Pe.current),l)break;return null;case 22:case 23:return t.lanes=0,ym(e,t,n);case 24:_n(t,$e,e.memoizedState.cache)}return pn(e,t,n)}function Rm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ie=!0;else{if(!cc(e,n)&&(t.flags&128)===0)return Ie=!1,Iy(e,t,n);Ie=(e.flags&131072)!==0}else Ie=!1,Oe&&(t.flags&1048576)!==0&&nd(t,gr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")yu(l)?(e=El(l,e),t.tag=1,t=Sm(null,t,l,e,n)):(t.tag=0,t=nc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===z){t.tag=11,t=pm(null,t,l,e,n);break e}else if(i===J){t.tag=14,t=vm(null,t,l,e,n);break e}}throw t=xe(l)||l,Error(o(306,t,""))}}return t;case 0:return nc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=El(l,t.pendingProps),Sm(e,t,l,i,n);case 3:e:{if(oe(t,t.stateNode.containerInfo),e===null)throw Error(o(387));l=t.pendingProps;var c=t.memoizedState;i=c.element,Du(e,t),ri(t,l,null,n);var h=t.memoizedState;if(l=h.cache,_n(t,$e,l),l!==c.cache&&Ru(t,[$e],n,!0),ii(),l=h.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=wm(e,t,l,n);break e}else if(l!==i){i=Ut(Error(o(424)),t),Fa(i),t=wm(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ye=Xt(e.firstChild),ht=t,Oe=!0,gl=null,Jt=!0,n=am(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Wa(),l===i){t=pn(e,t,n);break e}rt(e,t,l,n)}t=t.child}return t;case 26:return Hr(e,t),e===null?(n=Mh(t.type,null,t.pendingProps,null))?t.memoizedState=n:Oe||(n=t.type,e=t.pendingProps,l=Fr(re.current).createElement(n),l[st]=t,l[vt]=e,ut(l,n,e),Fe(l),t.stateNode=l):t.memoizedState=Mh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Re(t),e===null&&Oe&&(l=t.stateNode=Ch(t.type,t.pendingProps,re.current),ht=t,Jt=!0,i=Ye,Zn(t.type)?(Gc=i,Ye=Xt(l.firstChild)):Ye=i),rt(e,t,t.pendingProps.children,n),Hr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Oe&&((i=l=Ye)&&(l=C0(l,t.type,t.pendingProps,Jt),l!==null?(t.stateNode=l,ht=t,Ye=Xt(l.firstChild),Jt=!1,i=!0):i=!1),i||yl(t)),Re(t),i=t.type,c=t.pendingProps,h=e!==null?e.memoizedProps:null,l=c.children,kc(i,c)?l=null:h!==null&&kc(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=Bu(e,t,Qy,null,null,n),Ni._currentValue=i),Hr(e,t),rt(e,t,l,n),t.child;case 6:return e===null&&Oe&&((e=n=Ye)&&(n=N0(n,t.pendingProps,Jt),n!==null?(t.stateNode=n,ht=t,Ye=null,e=!0):e=!1),e||yl(t)),null;case 13:return Em(e,t,n);case 4:return oe(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=la(t,null,l,n):rt(e,t,l,n),t.child;case 11:return pm(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,_n(t,t.type,l.value),rt(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,xl(t),i=ft(i),l=l(i),t.flags|=1,rt(e,t,l,n),t.child;case 14:return vm(e,t,t.type,t.pendingProps,n);case 15:return gm(e,t,t.type,t.pendingProps,n);case 19:return Am(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=Br(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=un(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return ym(e,t,n);case 24:return xl(t),l=ft($e),e===null?(i=Ou(),i===null&&(i=He,c=Cu(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=n),i=c),t.memoizedState={parent:l,cache:i},_u(t),_n(t,$e,i)):((e.lanes&n)!==0&&(Du(e,t),ri(t,null,null,n),ii()),i=e.memoizedState,c=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),_n(t,$e,l)):(l=c.cache,_n(t,$e,l),l!==i.cache&&Ru(t,[$e],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function vn(e){e.flags|=4}function Cm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Uh(t)){if(t=kt.current,t!==null&&((Ce&4194048)===Ce?$t!==null:(Ce&62914560)!==Ce&&(Ce&536870912)===0||t!==$t))throw li=Mu,sd;e.flags|=8192}}function kr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?af():536870912,e.lanes|=t,oa|=t)}function mi(e,t){if(!Oe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function ke(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function e0(e,t,n){var l=t.pendingProps;switch(wu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ke(t),null;case 1:return ke(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),dn($e),Me(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Pa(t)?vn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,id())),ke(t),null;case 26:return n=t.memoizedState,e===null?(vn(t),n!==null?(ke(t),Cm(t,n)):(ke(t),t.flags&=-16777217)):n?n!==e.memoizedState?(vn(t),ke(t),Cm(t,n)):(ke(t),t.flags&=-16777217):(e.memoizedProps!==l&&vn(t),ke(t),t.flags&=-16777217),null;case 27:we(t),n=re.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&vn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return ke(t),null}e=I.current,Pa(t)?ld(t):(e=Ch(i,l,n),t.stateNode=e,vn(t))}return ke(t),null;case 5:if(we(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&vn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return ke(t),null}if(e=I.current,Pa(t))ld(t);else{switch(i=Fr(re.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[st]=t,e[vt]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ut(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&vn(t)}}return ke(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&vn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(e=re.current,Pa(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=ht,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[st]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||xh(e.nodeValue,n)),e||yl(t)}else e=Fr(e).createTextNode(l),e[st]=t,t.stateNode=e}return ke(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Pa(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(o(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[st]=t}else Wa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ke(t),i=!1}else i=id(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(hn(t),t):(hn(t),null)}if(hn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),kr(t,t.updateQueue),ke(t),null;case 4:return Me(),e===null&&jc(t.stateNode.containerInfo),ke(t),null;case 10:return dn(t.type),ke(t),null;case 19:if(W(Pe),i=t.memoizedState,i===null)return ke(t),null;if(l=(t.flags&128)!==0,c=i.rendering,c===null)if(l)mi(i,!1);else{if(Ve!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=jr(e),c!==null){for(t.flags|=128,mi(i,!1),e=c.updateQueue,t.updateQueue=e,kr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)td(n,e),n=n.sibling;return $(Pe,Pe.current&1|2),t.child}e=e.sibling}i.tail!==null&&ct()>Vr&&(t.flags|=128,l=!0,mi(i,!1),t.lanes=4194304)}else{if(!l)if(e=jr(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,kr(t,e),mi(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!Oe)return ke(t),null}else 2*ct()-i.renderingStartTime>Vr&&n!==536870912&&(t.flags|=128,l=!0,mi(i,!1),t.lanes=4194304);i.isBackwards?(c.sibling=t.child,t.child=c):(e=i.last,e!==null?e.sibling=c:t.child=c,i.last=c)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ct(),t.sibling=null,e=Pe.current,$(Pe,l?e&1|2:e&1),t):(ke(t),null);case 22:case 23:return hn(t),Lu(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(ke(t),t.subtreeFlags&6&&(t.flags|=8192)):ke(t),n=t.updateQueue,n!==null&&kr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&W(Sl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),dn($e),ke(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function t0(e,t){switch(wu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return dn($e),Me(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return we(t),null;case 13:if(hn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));Wa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(Pe),null;case 4:return Me(),null;case 10:return dn(t.type),null;case 22:case 23:return hn(t),Lu(),e!==null&&W(Sl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return dn($e),null;case 25:return null;default:return null}}function Nm(e,t){switch(wu(t),t.tag){case 3:dn($e),Me();break;case 26:case 27:case 5:we(t);break;case 4:Me();break;case 13:hn(t);break;case 19:W(Pe);break;case 10:dn(t.type);break;case 22:case 23:hn(t),Lu(),e!==null&&W(Sl);break;case 24:dn($e)}}function hi(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var c=n.create,h=n.inst;l=c(),h.destroy=l}n=n.next}while(n!==i)}}catch(g){Le(t,t.return,g)}}function Bn(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var c=i.next;l=c;do{if((l.tag&e)===e){var h=l.inst,g=h.destroy;if(g!==void 0){h.destroy=void 0,i=t;var T=n,H=g;try{H()}catch(V){Le(i,T,V)}}}l=l.next}while(l!==c)}}catch(V){Le(t,t.return,V)}}function Om(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{vd(t,n)}catch(l){Le(e,e.return,l)}}}function Mm(e,t,n){n.props=El(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){Le(e,t,l)}}function pi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){Le(e,t,i)}}function Pt(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){Le(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Le(e,t,i)}else n.current=null}function _m(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){Le(e,e.return,i)}}function sc(e,t,n){try{var l=e.stateNode;w0(l,e.type,n,t),l[vt]=t}catch(i){Le(e,e.return,i)}}function Dm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Zn(e.type)||e.tag===4}function fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Zn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function dc(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Wr));else if(l!==4&&(l===27&&Zn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(dc(e,t,n),e=e.sibling;e!==null;)dc(e,t,n),e=e.sibling}function qr(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Zn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(qr(e,t,n),e=e.sibling;e!==null;)qr(e,t,n),e=e.sibling}function zm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ut(t,l,n),t[st]=e,t[vt]=n}catch(c){Le(e,e.return,c)}}var gn=!1,Qe=!1,mc=!1,jm=typeof WeakSet=="function"?WeakSet:Set,et=null;function n0(e,t){if(e=e.containerInfo,Hc=ao,e=Qf(e),fu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var h=0,g=-1,T=-1,H=0,V=0,Q=e,B=null;t:for(;;){for(var k;Q!==n||i!==0&&Q.nodeType!==3||(g=h+i),Q!==c||l!==0&&Q.nodeType!==3||(T=h+l),Q.nodeType===3&&(h+=Q.nodeValue.length),(k=Q.firstChild)!==null;)B=Q,Q=k;for(;;){if(Q===e)break t;if(B===n&&++H===i&&(g=h),B===c&&++V===l&&(T=h),(k=Q.nextSibling)!==null)break;Q=B,B=Q.parentNode}Q=k}n=g===-1||T===-1?null:{start:g,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(Bc={focusedElem:e,selectionRange:n},ao=!1,et=t;et!==null;)if(t=et,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,et=e;else for(;et!==null;){switch(t=et,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,i=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var de=El(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(de,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(ce){Le(n,n.return,ce)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Yc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,et=e;break}et=t.return}}function Um(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:kn(e,n),l&4&&hi(5,n);break;case 1:if(kn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(h){Le(n,n.return,h)}else{var i=El(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Le(n,n.return,h)}}l&64&&Om(n),l&512&&pi(n,n.return);break;case 3:if(kn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{vd(e,t)}catch(h){Le(n,n.return,h)}}break;case 27:t===null&&l&4&&zm(n);case 26:case 5:kn(e,n),t===null&&l&4&&_m(n),l&512&&pi(n,n.return);break;case 12:kn(e,n);break;case 13:kn(e,n),l&4&&Bm(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=f0.bind(null,n),O0(e,n))));break;case 22:if(l=n.memoizedState!==null||gn,!l){t=t!==null&&t.memoizedState!==null||Qe,i=gn;var c=Qe;gn=l,(Qe=t)&&!c?qn(e,n,(n.subtreeFlags&8772)!==0):kn(e,n),gn=i,Qe=c}break;case 30:break;default:kn(e,n)}}function Lm(e){var t=e.alternate;t!==null&&(e.alternate=null,Lm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Zo(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Be=null,bt=!1;function yn(e,t,n){for(n=n.child;n!==null;)Hm(e,t,n),n=n.sibling}function Hm(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(La,n)}catch{}switch(n.tag){case 26:Qe||Pt(n,t),yn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Qe||Pt(n,t);var l=Be,i=bt;Zn(n.type)&&(Be=n.stateNode,bt=!1),yn(e,t,n),Ti(n.stateNode),Be=l,bt=i;break;case 5:Qe||Pt(n,t);case 6:if(l=Be,i=bt,Be=null,yn(e,t,n),Be=l,bt=i,Be!==null)if(bt)try{(Be.nodeType===9?Be.body:Be.nodeName==="HTML"?Be.ownerDocument.body:Be).removeChild(n.stateNode)}catch(c){Le(n,t,c)}else try{Be.removeChild(n.stateNode)}catch(c){Le(n,t,c)}break;case 18:Be!==null&&(bt?(e=Be,Ah(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Di(e)):Ah(Be,n.stateNode));break;case 4:l=Be,i=bt,Be=n.stateNode.containerInfo,bt=!0,yn(e,t,n),Be=l,bt=i;break;case 0:case 11:case 14:case 15:Qe||Bn(2,n,t),Qe||Bn(4,n,t),yn(e,t,n);break;case 1:Qe||(Pt(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Mm(n,t,l)),yn(e,t,n);break;case 21:yn(e,t,n);break;case 22:Qe=(l=Qe)||n.memoizedState!==null,yn(e,t,n),Qe=l;break;default:yn(e,t,n)}}function Bm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Di(e)}catch(n){Le(t,t.return,n)}}function l0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new jm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new jm),t;default:throw Error(o(435,e.tag))}}function hc(e,t){var n=l0(e);t.forEach(function(l){var i=d0.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function Rt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],c=e,h=t,g=h;e:for(;g!==null;){switch(g.tag){case 27:if(Zn(g.type)){Be=g.stateNode,bt=!1;break e}break;case 5:Be=g.stateNode,bt=!1;break e;case 3:case 4:Be=g.stateNode.containerInfo,bt=!0;break e}g=g.return}if(Be===null)throw Error(o(160));Hm(c,h,i),Be=null,bt=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)km(t,e),t=t.sibling}var Gt=null;function km(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rt(t,e),Ct(e),l&4&&(Bn(3,e,e.return),hi(3,e),Bn(5,e,e.return));break;case 1:Rt(t,e),Ct(e),l&512&&(Qe||n===null||Pt(n,n.return)),l&64&&gn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Gt;if(Rt(t,e),Ct(e),l&512&&(Qe||n===null||Pt(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":c=i.getElementsByTagName("title")[0],(!c||c[ka]||c[st]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(l),i.head.insertBefore(c,i.querySelector("head > title"))),ut(c,l,n),c[st]=e,Fe(c),l=c;break e;case"link":var h=zh("link","href",i).get(l+(n.href||""));if(h){for(var g=0;g<h.length;g++)if(c=h[g],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(g,1);break t}}c=i.createElement(l),ut(c,l,n),i.head.appendChild(c);break;case"meta":if(h=zh("meta","content",i).get(l+(n.content||""))){for(g=0;g<h.length;g++)if(c=h[g],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(g,1);break t}}c=i.createElement(l),ut(c,l,n),i.head.appendChild(c);break;default:throw Error(o(468,l))}c[st]=e,Fe(c),l=c}e.stateNode=l}else jh(i,e.type,e.stateNode);else e.stateNode=Dh(i,l,e.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?jh(i,e.type,e.stateNode):Dh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&sc(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rt(t,e),Ct(e),l&512&&(Qe||n===null||Pt(n,n.return)),n!==null&&l&4&&sc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rt(t,e),Ct(e),l&512&&(Qe||n===null||Pt(n,n.return)),e.flags&32){i=e.stateNode;try{kl(i,"")}catch(k){Le(e,e.return,k)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,sc(e,i,n!==null?n.memoizedProps:i)),l&1024&&(mc=!0);break;case 6:if(Rt(t,e),Ct(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(k){Le(e,e.return,k)}}break;case 3:if(to=null,i=Gt,Gt=Ir(t.containerInfo),Rt(t,e),Gt=i,Ct(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Di(t.containerInfo)}catch(k){Le(e,e.return,k)}mc&&(mc=!1,qm(e));break;case 4:l=Gt,Gt=Ir(e.stateNode.containerInfo),Rt(t,e),Ct(e),Gt=l;break;case 12:Rt(t,e),Ct(e);break;case 13:Rt(t,e),Ct(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(xc=ct()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,hc(e,l)));break;case 22:i=e.memoizedState!==null;var T=n!==null&&n.memoizedState!==null,H=gn,V=Qe;if(gn=H||i,Qe=V||T,Rt(t,e),Qe=V,gn=H,Ct(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||T||gn||Qe||Tl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){T=n=t;try{if(c=T.stateNode,i)h=c.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{g=T.stateNode;var Q=T.memoizedProps.style,B=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;g.style.display=B==null||typeof B=="boolean"?"":(""+B).trim()}}catch(k){Le(T,T.return,k)}}}else if(t.tag===6){if(n===null){T=t;try{T.stateNode.nodeValue=i?"":T.memoizedProps}catch(k){Le(T,T.return,k)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,hc(e,n))));break;case 19:Rt(t,e),Ct(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,hc(e,l)));break;case 30:break;case 21:break;default:Rt(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(Dm(l)){n=l;break}l=l.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode,c=fc(e);qr(e,c,i);break;case 5:var h=n.stateNode;n.flags&32&&(kl(h,""),n.flags&=-33);var g=fc(e);qr(e,g,h);break;case 3:case 4:var T=n.stateNode.containerInfo,H=fc(e);dc(e,H,T);break;default:throw Error(o(161))}}catch(V){Le(e,e.return,V)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function qm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;qm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function kn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Um(e,t.alternate,t),t=t.sibling}function Tl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Bn(4,t,t.return),Tl(t);break;case 1:Pt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Mm(t,t.return,n),Tl(t);break;case 27:Ti(t.stateNode);case 26:case 5:Pt(t,t.return),Tl(t);break;case 22:t.memoizedState===null&&Tl(t);break;case 30:Tl(t);break;default:Tl(t)}e=e.sibling}}function qn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,c=t,h=c.flags;switch(c.tag){case 0:case 11:case 15:qn(i,c,n),hi(4,c);break;case 1:if(qn(i,c,n),l=c,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(H){Le(l,l.return,H)}if(l=c,i=l.updateQueue,i!==null){var g=l.stateNode;try{var T=i.shared.hiddenCallbacks;if(T!==null)for(i.shared.hiddenCallbacks=null,i=0;i<T.length;i++)pd(T[i],g)}catch(H){Le(l,l.return,H)}}n&&h&64&&Om(c),pi(c,c.return);break;case 27:zm(c);case 26:case 5:qn(i,c,n),n&&l===null&&h&4&&_m(c),pi(c,c.return);break;case 12:qn(i,c,n);break;case 13:qn(i,c,n),n&&h&4&&Bm(i,c);break;case 22:c.memoizedState===null&&qn(i,c,n),pi(c,c.return);break;case 30:break;default:qn(i,c,n)}t=t.sibling}}function pc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&ei(n))}function vc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ei(e))}function Wt(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ym(e,t,n,l),t=t.sibling}function Ym(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Wt(e,t,n,l),i&2048&&hi(9,t);break;case 1:Wt(e,t,n,l);break;case 3:Wt(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ei(e)));break;case 12:if(i&2048){Wt(e,t,n,l),e=t.stateNode;try{var c=t.memoizedProps,h=c.id,g=c.onPostCommit;typeof g=="function"&&g(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(T){Le(t,t.return,T)}}else Wt(e,t,n,l);break;case 13:Wt(e,t,n,l);break;case 23:break;case 22:c=t.stateNode,h=t.alternate,t.memoizedState!==null?c._visibility&2?Wt(e,t,n,l):vi(e,t):c._visibility&2?Wt(e,t,n,l):(c._visibility|=2,aa(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&pc(h,t);break;case 24:Wt(e,t,n,l),i&2048&&vc(t.alternate,t);break;default:Wt(e,t,n,l)}}function aa(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,h=t,g=n,T=l,H=h.flags;switch(h.tag){case 0:case 11:case 15:aa(c,h,g,T,i),hi(8,h);break;case 23:break;case 22:var V=h.stateNode;h.memoizedState!==null?V._visibility&2?aa(c,h,g,T,i):vi(c,h):(V._visibility|=2,aa(c,h,g,T,i)),i&&H&2048&&pc(h.alternate,h);break;case 24:aa(c,h,g,T,i),i&&H&2048&&vc(h.alternate,h);break;default:aa(c,h,g,T,i)}t=t.sibling}}function vi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:vi(n,l),i&2048&&pc(l.alternate,l);break;case 24:vi(n,l),i&2048&&vc(l.alternate,l);break;default:vi(n,l)}t=t.sibling}}var gi=8192;function ia(e){if(e.subtreeFlags&gi)for(e=e.child;e!==null;)Vm(e),e=e.sibling}function Vm(e){switch(e.tag){case 26:ia(e),e.flags&gi&&e.memoizedState!==null&&V0(Gt,e.memoizedState,e.memoizedProps);break;case 5:ia(e);break;case 3:case 4:var t=Gt;Gt=Ir(e.stateNode.containerInfo),ia(e),Gt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=gi,gi=16777216,ia(e),gi=t):ia(e));break;default:ia(e)}}function Gm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function yi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];et=l,Qm(l,e)}Gm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xm(e),e=e.sibling}function Xm(e){switch(e.tag){case 0:case 11:case 15:yi(e),e.flags&2048&&Bn(9,e,e.return);break;case 3:yi(e);break;case 12:yi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Yr(e)):yi(e);break;default:yi(e)}}function Yr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];et=l,Qm(l,e)}Gm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Bn(8,t,t.return),Yr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Yr(t));break;default:Yr(t)}e=e.sibling}}function Qm(e,t){for(;et!==null;){var n=et;switch(n.tag){case 0:case 11:case 15:Bn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ei(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,et=l;else e:for(n=e;et!==null;){l=et;var i=l.sibling,c=l.return;if(Lm(l),l===n){et=null;break e}if(i!==null){i.return=c,et=i;break e}et=c}}}var a0={getCacheForType:function(e){var t=ft($e),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},i0=typeof WeakMap=="function"?WeakMap:Map,_e=0,He=null,Te=null,Ce=0,De=0,Nt=null,Yn=!1,ra=!1,gc=!1,bn=0,Ve=0,Vn=0,Al=0,yc=0,qt=0,oa=0,bi=null,xt=null,bc=!1,xc=0,Vr=1/0,Gr=null,Gn=null,ot=0,Xn=null,ua=null,ca=0,Sc=0,wc=null,Zm=null,xi=0,Ec=null;function Ot(){if((_e&2)!==0&&Ce!==0)return Ce&-Ce;if(L.T!==null){var e=Pl;return e!==0?e:Mc()}return uf()}function Km(){qt===0&&(qt=(Ce&536870912)===0||Oe?lf():536870912);var e=kt.current;return e!==null&&(e.flags|=32),qt}function Mt(e,t,n){(e===He&&(De===2||De===9)||e.cancelPendingCommit!==null)&&(sa(e,0),Qn(e,Ce,qt,!1)),Ba(e,n),((_e&2)===0||e!==He)&&(e===He&&((_e&2)===0&&(Al|=n),Ve===4&&Qn(e,Ce,qt,!1)),Ft(e))}function Jm(e,t,n){if((_e&6)!==0)throw Error(o(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Ha(e,t),i=l?u0(e,t):Rc(e,t,!0),c=l;do{if(i===0){ra&&!l&&Qn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!r0(n)){i=Rc(e,t,!1),c=!1;continue}if(i===2){if(c=t,e.errorRecoveryDisabledLanes&c)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var g=e;i=bi;var T=g.current.memoizedState.isDehydrated;if(T&&(sa(g,h).flags|=256),h=Rc(g,h,!1),h!==2){if(gc&&!T){g.errorRecoveryDisabledLanes|=c,Al|=c,i=4;break e}c=xt,xt=i,c!==null&&(xt===null?xt=c:xt.push.apply(xt,c))}i=h}if(c=!1,i!==2)continue}}if(i===1){sa(e,0),Qn(e,t,0,!0);break}e:{switch(l=e,c=i,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Qn(l,t,qt,!Yn);break e;case 2:xt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(i=xc+300-ct(),10<i)){if(Qn(l,t,qt,!Yn),er(l,0,!0)!==0)break e;l.timeoutHandle=Eh($m.bind(null,l,n,xt,Gr,bc,t,qt,Al,oa,Yn,c,2,-0,0),i);break e}$m(l,n,xt,Gr,bc,t,qt,Al,oa,Yn,c,0,-0,0)}}break}while(!0);Ft(e)}function $m(e,t,n,l,i,c,h,g,T,H,V,Q,B,k){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Ci={stylesheets:null,count:0,unsuspend:Y0},Vm(t),Q=G0(),Q!==null)){e.cancelPendingCommit=Q(nh.bind(null,e,t,c,n,l,i,h,g,T,V,1,B,k)),Qn(e,c,h,!H);return}nh(e,t,c,n,l,i,h,g,T)}function r0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],c=i.getSnapshot;i=i.value;try{if(!Tt(c(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Qn(e,t,n,l){t&=~yc,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var c=31-Et(i),h=1<<c;l[c]=-1,i&=~h}n!==0&&rf(e,n,t)}function Xr(){return(_e&6)===0?(Si(0),!1):!0}function Tc(){if(Te!==null){if(De===0)var e=Te.return;else e=Te,fn=bl=null,Yu(e),na=null,fi=0,e=Te;for(;e!==null;)Nm(e.alternate,e),e=e.return;Te=null}}function sa(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,T0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Tc(),He=e,Te=n=un(e.current,null),Ce=t,De=0,Nt=null,Yn=!1,ra=Ha(e,t),gc=!1,oa=qt=yc=Al=Vn=Ve=0,xt=bi=null,bc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-Et(l),c=1<<i;t|=e[i],l&=~c}return bn=t,dr(),n}function Pm(e,t){ye=null,L.H=_r,t===ni||t===Sr?(t=md(),De=3):t===sd?(t=md(),De=4):De=t===hm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Nt=t,Te===null&&(Ve=1,Lr(e,Ut(t,e.current)))}function Wm(){var e=L.H;return L.H=_r,e===null?_r:e}function Fm(){var e=L.A;return L.A=a0,e}function Ac(){Ve=4,Yn||(Ce&4194048)!==Ce&&kt.current!==null||(ra=!0),(Vn&134217727)===0&&(Al&134217727)===0||He===null||Qn(He,Ce,qt,!1)}function Rc(e,t,n){var l=_e;_e|=2;var i=Wm(),c=Fm();(He!==e||Ce!==t)&&(Gr=null,sa(e,t)),t=!1;var h=Ve;e:do try{if(De!==0&&Te!==null){var g=Te,T=Nt;switch(De){case 8:Tc(),h=6;break e;case 3:case 2:case 9:case 6:kt.current===null&&(t=!0);var H=De;if(De=0,Nt=null,fa(e,g,T,H),n&&ra){h=0;break e}break;default:H=De,De=0,Nt=null,fa(e,g,T,H)}}o0(),h=Ve;break}catch(V){Pm(e,V)}while(!0);return t&&e.shellSuspendCounter++,fn=bl=null,_e=l,L.H=i,L.A=c,Te===null&&(He=null,Ce=0,dr()),h}function o0(){for(;Te!==null;)Im(Te)}function u0(e,t){var n=_e;_e|=2;var l=Wm(),i=Fm();He!==e||Ce!==t?(Gr=null,Vr=ct()+500,sa(e,t)):ra=Ha(e,t);e:do try{if(De!==0&&Te!==null){t=Te;var c=Nt;t:switch(De){case 1:De=0,Nt=null,fa(e,t,c,1);break;case 2:case 9:if(fd(c)){De=0,Nt=null,eh(t);break}t=function(){De!==2&&De!==9||He!==e||(De=7),Ft(e)},c.then(t,t);break e;case 3:De=7;break e;case 4:De=5;break e;case 7:fd(c)?(De=0,Nt=null,eh(t)):(De=0,Nt=null,fa(e,t,c,7));break;case 5:var h=null;switch(Te.tag){case 26:h=Te.memoizedState;case 5:case 27:var g=Te;if(!h||Uh(h)){De=0,Nt=null;var T=g.sibling;if(T!==null)Te=T;else{var H=g.return;H!==null?(Te=H,Qr(H)):Te=null}break t}}De=0,Nt=null,fa(e,t,c,5);break;case 6:De=0,Nt=null,fa(e,t,c,6);break;case 8:Tc(),Ve=6;break e;default:throw Error(o(462))}}c0();break}catch(V){Pm(e,V)}while(!0);return fn=bl=null,L.H=l,L.A=i,_e=n,Te!==null?0:(He=null,Ce=0,dr(),Ve)}function c0(){for(;Te!==null&&!rl();)Im(Te)}function Im(e){var t=Rm(e.alternate,e,bn);e.memoizedProps=e.pendingProps,t===null?Qr(e):Te=t}function eh(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=xm(n,t,t.pendingProps,t.type,void 0,Ce);break;case 11:t=xm(n,t,t.pendingProps,t.type.render,t.ref,Ce);break;case 5:Yu(t);default:Nm(n,t),t=Te=td(t,bn),t=Rm(n,t,bn)}e.memoizedProps=e.pendingProps,t===null?Qr(e):Te=t}function fa(e,t,n,l){fn=bl=null,Yu(t),na=null,fi=0;var i=t.return;try{if(Fy(e,i,t,n,Ce)){Ve=1,Lr(e,Ut(n,e.current)),Te=null;return}}catch(c){if(i!==null)throw Te=i,c;Ve=1,Lr(e,Ut(n,e.current)),Te=null;return}t.flags&32768?(Oe||l===1?e=!0:ra||(Ce&536870912)!==0?e=!1:(Yn=e=!0,(l===2||l===9||l===3||l===6)&&(l=kt.current,l!==null&&l.tag===13&&(l.flags|=16384))),th(t,e)):Qr(t)}function Qr(e){var t=e;do{if((t.flags&32768)!==0){th(t,Yn);return}e=t.return;var n=e0(t.alternate,t,bn);if(n!==null){Te=n;return}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);Ve===0&&(Ve=5)}function th(e,t){do{var n=t0(e.alternate,e);if(n!==null){n.flags&=32767,Te=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Te=e;return}Te=e=n}while(e!==null);Ve=6,Te=null}function nh(e,t,n,l,i,c,h,g,T){e.cancelPendingCommit=null;do Zr();while(ot!==0);if((_e&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=vu,Yg(e,n,c,h,g,T),e===He&&(Te=He=null,Ce=0),ua=t,Xn=e,ca=n,Sc=c,wc=i,Zm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,m0(Wi,function(){return oh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=L.T,L.T=null,i=Z.p,Z.p=2,h=_e,_e|=4;try{n0(e,t,n)}finally{_e=h,Z.p=i,L.T=l}}ot=1,lh(),ah(),ih()}}function lh(){if(ot===1){ot=0;var e=Xn,t=ua,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=L.T,L.T=null;var l=Z.p;Z.p=2;var i=_e;_e|=4;try{km(t,e);var c=Bc,h=Qf(e.containerInfo),g=c.focusedElem,T=c.selectionRange;if(h!==g&&g&&g.ownerDocument&&Xf(g.ownerDocument.documentElement,g)){if(T!==null&&fu(g)){var H=T.start,V=T.end;if(V===void 0&&(V=H),"selectionStart"in g)g.selectionStart=H,g.selectionEnd=Math.min(V,g.value.length);else{var Q=g.ownerDocument||document,B=Q&&Q.defaultView||window;if(B.getSelection){var k=B.getSelection(),de=g.textContent.length,ce=Math.min(T.start,de),Ue=T.end===void 0?ce:Math.min(T.end,de);!k.extend&&ce>Ue&&(h=Ue,Ue=ce,ce=h);var j=Gf(g,ce),_=Gf(g,Ue);if(j&&_&&(k.rangeCount!==1||k.anchorNode!==j.node||k.anchorOffset!==j.offset||k.focusNode!==_.node||k.focusOffset!==_.offset)){var U=Q.createRange();U.setStart(j.node,j.offset),k.removeAllRanges(),ce>Ue?(k.addRange(U),k.extend(_.node,_.offset)):(U.setEnd(_.node,_.offset),k.addRange(U))}}}}for(Q=[],k=g;k=k.parentNode;)k.nodeType===1&&Q.push({element:k,left:k.scrollLeft,top:k.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<Q.length;g++){var X=Q[g];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}ao=!!Hc,Bc=Hc=null}finally{_e=i,Z.p=l,L.T=n}}e.current=t,ot=2}}function ah(){if(ot===2){ot=0;var e=Xn,t=ua,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=L.T,L.T=null;var l=Z.p;Z.p=2;var i=_e;_e|=4;try{Um(e,t.alternate,t)}finally{_e=i,Z.p=l,L.T=n}}ot=3}}function ih(){if(ot===4||ot===3){ot=0,ol();var e=Xn,t=ua,n=ca,l=Zm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,ua=Xn=null,rh(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Gn=null),Xo(n),t=t.stateNode,wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(La,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=L.T,i=Z.p,Z.p=2,L.T=null;try{for(var c=e.onRecoverableError,h=0;h<l.length;h++){var g=l[h];c(g.value,{componentStack:g.stack})}}finally{L.T=t,Z.p=i}}(ca&3)!==0&&Zr(),Ft(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===Ec?xi++:(xi=0,Ec=e):xi=0,Si(0)}}function rh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,ei(t)))}function Zr(e){return lh(),ah(),ih(),oh()}function oh(){if(ot!==5)return!1;var e=Xn,t=Sc;Sc=0;var n=Xo(ca),l=L.T,i=Z.p;try{Z.p=32>n?32:n,L.T=null,n=wc,wc=null;var c=Xn,h=ca;if(ot=0,ua=Xn=null,ca=0,(_e&6)!==0)throw Error(o(331));var g=_e;if(_e|=4,Xm(c.current),Ym(c,c.current,h,n),_e=g,Si(0,!1),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(La,c)}catch{}return!0}finally{Z.p=i,L.T=l,rh(e,t)}}function uh(e,t,n){t=Ut(n,t),t=tc(e.stateNode,t,2),e=jn(e,t,2),e!==null&&(Ba(e,2),Ft(e))}function Le(e,t,n){if(e.tag===3)uh(e,e,n);else for(;t!==null;){if(t.tag===3){uh(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Gn===null||!Gn.has(l))){e=Ut(n,e),n=dm(2),l=jn(t,n,2),l!==null&&(mm(n,l,t,e),Ba(l,2),Ft(l));break}}t=t.return}}function Cc(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new i0;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(gc=!0,i.add(n),e=s0.bind(null,e,t,n),t.then(e,e))}function s0(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,He===e&&(Ce&n)===n&&(Ve===4||Ve===3&&(Ce&62914560)===Ce&&300>ct()-xc?(_e&2)===0&&sa(e,0):yc|=n,oa===Ce&&(oa=0)),Ft(e)}function ch(e,t){t===0&&(t=af()),e=Zl(e,t),e!==null&&(Ba(e,t),Ft(e))}function f0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ch(e,n)}function d0(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(t),ch(e,n)}function m0(e,t){return it(e,t)}var Kr=null,da=null,Nc=!1,Jr=!1,Oc=!1,Rl=0;function Ft(e){e!==da&&e.next===null&&(da===null?Kr=da=e:da=da.next=e),Jr=!0,Nc||(Nc=!0,p0())}function Si(e,t){if(!Oc&&Jr){Oc=!0;do for(var n=!1,l=Kr;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var c=0;else{var h=l.suspendedLanes,g=l.pingedLanes;c=(1<<31-Et(42|e)+1)-1,c&=i&~(h&~g),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,mh(l,c))}else c=Ce,c=er(l,l===He?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Ha(l,c)||(n=!0,mh(l,c));l=l.next}while(n);Oc=!1}}function h0(){sh()}function sh(){Jr=Nc=!1;var e=0;Rl!==0&&(E0()&&(e=Rl),Rl=0);for(var t=ct(),n=null,l=Kr;l!==null;){var i=l.next,c=fh(l,t);c===0?(l.next=null,n===null?Kr=i:n.next=i,i===null&&(da=n)):(n=l,(e!==0||(c&3)!==0)&&(Jr=!0)),l=i}Si(e)}function fh(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var h=31-Et(c),g=1<<h,T=i[h];T===-1?((g&n)===0||(g&l)!==0)&&(i[h]=qg(g,t)):T<=t&&(e.expiredLanes|=g),c&=~g}if(t=He,n=Ce,n=er(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(De===2||De===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&pt(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Ha(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&pt(l),Xo(n)){case 2:case 8:n=tf;break;case 32:n=Wi;break;case 268435456:n=nf;break;default:n=Wi}return l=dh.bind(null,e),n=it(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&pt(l),e.callbackPriority=2,e.callbackNode=null,2}function dh(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Zr()&&e.callbackNode!==n)return null;var l=Ce;return l=er(e,e===He?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Jm(e,l,t),fh(e,ct()),e.callbackNode!=null&&e.callbackNode===n?dh.bind(null,e):null)}function mh(e,t){if(Zr())return null;Jm(e,t,!0)}function p0(){A0(function(){(_e&6)!==0?it(ul,h0):sh()})}function Mc(){return Rl===0&&(Rl=lf()),Rl}function hh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ir(""+e)}function ph(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function v0(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var c=hh((i[vt]||null).action),h=l.submitter;h&&(t=(t=h[vt]||null)?hh(t.formAction):h.getAttribute("formAction"),t!==null&&(c=t,h=null));var g=new cr("action","action",null,l,i);e.push({event:g,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Rl!==0){var T=h?ph(i,h):new FormData(i);Pu(n,{pending:!0,data:T,method:i.method,action:c},null,T)}}else typeof c=="function"&&(g.preventDefault(),T=h?ph(i,h):new FormData(i),Pu(n,{pending:!0,data:T,method:i.method,action:c},c,T))},currentTarget:i}]})}}for(var _c=0;_c<pu.length;_c++){var Dc=pu[_c],g0=Dc.toLowerCase(),y0=Dc[0].toUpperCase()+Dc.slice(1);Vt(g0,"on"+y0)}Vt(Jf,"onAnimationEnd"),Vt($f,"onAnimationIteration"),Vt(Pf,"onAnimationStart"),Vt("dblclick","onDoubleClick"),Vt("focusin","onFocus"),Vt("focusout","onBlur"),Vt(Uy,"onTransitionRun"),Vt(Ly,"onTransitionStart"),Vt(Hy,"onTransitionCancel"),Vt(Wf,"onTransitionEnd"),Ll("onMouseEnter",["mouseout","mouseover"]),Ll("onMouseLeave",["mouseout","mouseover"]),Ll("onPointerEnter",["pointerout","pointerover"]),Ll("onPointerLeave",["pointerout","pointerover"]),sl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),sl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),sl("onBeforeInput",["compositionend","keypress","textInput","paste"]),sl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),sl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),sl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var wi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),b0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(wi));function vh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var h=l.length-1;0<=h;h--){var g=l[h],T=g.instance,H=g.currentTarget;if(g=g.listener,T!==c&&i.isPropagationStopped())break e;c=g,i.currentTarget=H;try{c(i)}catch(V){Ur(V)}i.currentTarget=null,c=T}else for(h=0;h<l.length;h++){if(g=l[h],T=g.instance,H=g.currentTarget,g=g.listener,T!==c&&i.isPropagationStopped())break e;c=g,i.currentTarget=H;try{c(i)}catch(V){Ur(V)}i.currentTarget=null,c=T}}}}function Ae(e,t){var n=t[Qo];n===void 0&&(n=t[Qo]=new Set);var l=e+"__bubble";n.has(l)||(gh(t,e,2,!1),n.add(l))}function zc(e,t,n){var l=0;t&&(l|=4),gh(n,e,l,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function jc(e){if(!e[$r]){e[$r]=!0,sf.forEach(function(n){n!=="selectionchange"&&(b0.has(n)||zc(n,!1,e),zc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[$r]||(t[$r]=!0,zc("selectionchange",!1,t))}}function gh(e,t,n,l){switch(Yh(t)){case 2:var i=Z0;break;case 8:i=K0;break;default:i=Jc}n=i.bind(null,t,n,e),i=void 0,!nu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Uc(e,t,n,l,i){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var g=l.stateNode.containerInfo;if(g===i)break;if(h===4)for(h=l.return;h!==null;){var T=h.tag;if((T===3||T===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;g!==null;){if(h=zl(g),h===null)return;if(T=h.tag,T===5||T===6||T===26||T===27){l=c=h;continue e}g=g.parentNode}}l=l.return}Tf(function(){var H=c,V=eu(n),Q=[];e:{var B=Ff.get(e);if(B!==void 0){var k=cr,de=e;switch(e){case"keypress":if(or(n)===0)break e;case"keydown":case"keyup":k=my;break;case"focusin":de="focus",k=ru;break;case"focusout":de="blur",k=ru;break;case"beforeblur":case"afterblur":k=ru;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":k=Cf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":k=ty;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":k=vy;break;case Jf:case $f:case Pf:k=ay;break;case Wf:k=yy;break;case"scroll":case"scrollend":k=Ig;break;case"wheel":k=xy;break;case"copy":case"cut":case"paste":k=ry;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":k=Of;break;case"toggle":case"beforetoggle":k=wy}var ce=(t&4)!==0,Ue=!ce&&(e==="scroll"||e==="scrollend"),j=ce?B!==null?B+"Capture":null:B;ce=[];for(var _=H,U;_!==null;){var X=_;if(U=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||U===null||j===null||(X=Ya(_,j),X!=null&&ce.push(Ei(_,X,U))),Ue)break;_=_.return}0<ce.length&&(B=new k(B,de,null,n,V),Q.push({event:B,listeners:ce}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",k=e==="mouseout"||e==="pointerout",B&&n!==Io&&(de=n.relatedTarget||n.fromElement)&&(zl(de)||de[Dl]))break e;if((k||B)&&(B=V.window===V?V:(B=V.ownerDocument)?B.defaultView||B.parentWindow:window,k?(de=n.relatedTarget||n.toElement,k=H,de=de?zl(de):null,de!==null&&(Ue=f(de),ce=de.tag,de!==Ue||ce!==5&&ce!==27&&ce!==6)&&(de=null)):(k=null,de=H),k!==de)){if(ce=Cf,X="onMouseLeave",j="onMouseEnter",_="mouse",(e==="pointerout"||e==="pointerover")&&(ce=Of,X="onPointerLeave",j="onPointerEnter",_="pointer"),Ue=k==null?B:qa(k),U=de==null?B:qa(de),B=new ce(X,_+"leave",k,n,V),B.target=Ue,B.relatedTarget=U,X=null,zl(V)===H&&(ce=new ce(j,_+"enter",de,n,V),ce.target=U,ce.relatedTarget=Ue,X=ce),Ue=X,k&&de)t:{for(ce=k,j=de,_=0,U=ce;U;U=ma(U))_++;for(U=0,X=j;X;X=ma(X))U++;for(;0<_-U;)ce=ma(ce),_--;for(;0<U-_;)j=ma(j),U--;for(;_--;){if(ce===j||j!==null&&ce===j.alternate)break t;ce=ma(ce),j=ma(j)}ce=null}else ce=null;k!==null&&yh(Q,B,k,ce,!1),de!==null&&Ue!==null&&yh(Q,Ue,de,ce,!0)}}e:{if(B=H?qa(H):window,k=B.nodeName&&B.nodeName.toLowerCase(),k==="select"||k==="input"&&B.type==="file")var te=Hf;else if(Uf(B))if(Bf)te=Dy;else{te=My;var Se=Oy}else k=B.nodeName,!k||k.toLowerCase()!=="input"||B.type!=="checkbox"&&B.type!=="radio"?H&&Fo(H.elementType)&&(te=Hf):te=_y;if(te&&(te=te(e,H))){Lf(Q,te,n,V);break e}Se&&Se(e,B,H),e==="focusout"&&H&&B.type==="number"&&H.memoizedProps.value!=null&&Wo(B,"number",B.value)}switch(Se=H?qa(H):window,e){case"focusin":(Uf(Se)||Se.contentEditable==="true")&&(Gl=Se,du=H,$a=null);break;case"focusout":$a=du=Gl=null;break;case"mousedown":mu=!0;break;case"contextmenu":case"mouseup":case"dragend":mu=!1,Zf(Q,n,V);break;case"selectionchange":if(jy)break;case"keydown":case"keyup":Zf(Q,n,V)}var ie;if(uu)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else Vl?zf(e,n)&&(se="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(se="onCompositionStart");se&&(Mf&&n.locale!=="ko"&&(Vl||se!=="onCompositionStart"?se==="onCompositionEnd"&&Vl&&(ie=Af()):(Mn=V,lu="value"in Mn?Mn.value:Mn.textContent,Vl=!0)),Se=Pr(H,se),0<Se.length&&(se=new Nf(se,e,null,n,V),Q.push({event:se,listeners:Se}),ie?se.data=ie:(ie=jf(n),ie!==null&&(se.data=ie)))),(ie=Ty?Ay(e,n):Ry(e,n))&&(se=Pr(H,"onBeforeInput"),0<se.length&&(Se=new Nf("onBeforeInput","beforeinput",null,n,V),Q.push({event:Se,listeners:se}),Se.data=ie)),v0(Q,e,H,n,V)}vh(Q,t)})}function Ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Pr(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=Ya(e,n),i!=null&&l.unshift(Ei(e,i,c)),i=Ya(e,t),i!=null&&l.push(Ei(e,i,c))),e.tag===3)return l;e=e.return}return[]}function ma(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function yh(e,t,n,l,i){for(var c=t._reactName,h=[];n!==null&&n!==l;){var g=n,T=g.alternate,H=g.stateNode;if(g=g.tag,T!==null&&T===l)break;g!==5&&g!==26&&g!==27||H===null||(T=H,i?(H=Ya(n,c),H!=null&&h.unshift(Ei(n,H,T))):i||(H=Ya(n,c),H!=null&&h.push(Ei(n,H,T)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var x0=/\r\n?/g,S0=/\u0000|\uFFFD/g;function bh(e){return(typeof e=="string"?e:""+e).replace(x0,`
`).replace(S0,"")}function xh(e,t){return t=bh(t),bh(e)===t}function Wr(){}function je(e,t,n,l,i,c){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||kl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&kl(e,""+l);break;case"className":nr(e,"class",l);break;case"tabIndex":nr(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":nr(e,n,l);break;case"style":wf(e,l,c);break;case"data":if(t!=="object"){nr(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ir(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&je(e,t,"name",i.name,i,null),je(e,t,"formEncType",i.formEncType,i,null),je(e,t,"formMethod",i.formMethod,i,null),je(e,t,"formTarget",i.formTarget,i,null)):(je(e,t,"encType",i.encType,i,null),je(e,t,"method",i.method,i,null),je(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ir(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=Wr);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=ir(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Ae("beforetoggle",e),Ae("toggle",e),tr(e,"popover",l);break;case"xlinkActuate":rn(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":rn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":rn(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":rn(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":rn(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":rn(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":rn(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":rn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":rn(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":tr(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Wg.get(n)||n,tr(e,n,l))}}function Lc(e,t,n,l,i,c){switch(n){case"style":wf(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof l=="string"?kl(e,l):(typeof l=="number"||typeof l=="bigint")&&kl(e,""+l);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Wr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ff.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),c=e[vt]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,i),typeof l=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):tr(e,n,l)}}}function ut(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ae("error",e),Ae("load",e);var l=!1,i=!1,c;for(c in n)if(n.hasOwnProperty(c)){var h=n[c];if(h!=null)switch(c){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:je(e,t,c,h,n,null)}}i&&je(e,t,"srcSet",n.srcSet,n,null),l&&je(e,t,"src",n.src,n,null);return;case"input":Ae("invalid",e);var g=c=h=i=null,T=null,H=null;for(l in n)if(n.hasOwnProperty(l)){var V=n[l];if(V!=null)switch(l){case"name":i=V;break;case"type":h=V;break;case"checked":T=V;break;case"defaultChecked":H=V;break;case"value":c=V;break;case"defaultValue":g=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(o(137,t));break;default:je(e,t,l,V,n,null)}}yf(e,c,g,T,H,h,i,!1),lr(e);return;case"select":Ae("invalid",e),l=h=c=null;for(i in n)if(n.hasOwnProperty(i)&&(g=n[i],g!=null))switch(i){case"value":c=g;break;case"defaultValue":h=g;break;case"multiple":l=g;default:je(e,t,i,g,n,null)}t=c,n=h,e.multiple=!!l,t!=null?Bl(e,!!l,t,!1):n!=null&&Bl(e,!!l,n,!0);return;case"textarea":Ae("invalid",e),c=i=l=null;for(h in n)if(n.hasOwnProperty(h)&&(g=n[h],g!=null))switch(h){case"value":l=g;break;case"defaultValue":i=g;break;case"children":c=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(o(91));break;default:je(e,t,h,g,n,null)}xf(e,l,i,c),lr(e);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(l=n[T],l!=null))switch(T){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:je(e,t,T,l,n,null)}return;case"dialog":Ae("beforetoggle",e),Ae("toggle",e),Ae("cancel",e),Ae("close",e);break;case"iframe":case"object":Ae("load",e);break;case"video":case"audio":for(l=0;l<wi.length;l++)Ae(wi[l],e);break;case"image":Ae("error",e),Ae("load",e);break;case"details":Ae("toggle",e);break;case"embed":case"source":case"link":Ae("error",e),Ae("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(H in n)if(n.hasOwnProperty(H)&&(l=n[H],l!=null))switch(H){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:je(e,t,H,l,n,null)}return;default:if(Fo(t)){for(V in n)n.hasOwnProperty(V)&&(l=n[V],l!==void 0&&Lc(e,t,V,l,n,void 0));return}}for(g in n)n.hasOwnProperty(g)&&(l=n[g],l!=null&&je(e,t,g,l,n,null))}function w0(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,h=null,g=null,T=null,H=null,V=null;for(k in n){var Q=n[k];if(n.hasOwnProperty(k)&&Q!=null)switch(k){case"checked":break;case"value":break;case"defaultValue":T=Q;default:l.hasOwnProperty(k)||je(e,t,k,null,l,Q)}}for(var B in l){var k=l[B];if(Q=n[B],l.hasOwnProperty(B)&&(k!=null||Q!=null))switch(B){case"type":c=k;break;case"name":i=k;break;case"checked":H=k;break;case"defaultChecked":V=k;break;case"value":h=k;break;case"defaultValue":g=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(o(137,t));break;default:k!==Q&&je(e,t,B,k,l,Q)}}Po(e,h,g,T,H,V,c,i);return;case"select":k=h=g=B=null;for(c in n)if(T=n[c],n.hasOwnProperty(c)&&T!=null)switch(c){case"value":break;case"multiple":k=T;default:l.hasOwnProperty(c)||je(e,t,c,null,l,T)}for(i in l)if(c=l[i],T=n[i],l.hasOwnProperty(i)&&(c!=null||T!=null))switch(i){case"value":B=c;break;case"defaultValue":g=c;break;case"multiple":h=c;default:c!==T&&je(e,t,i,c,l,T)}t=g,n=h,l=k,B!=null?Bl(e,!!n,B,!1):!!l!=!!n&&(t!=null?Bl(e,!!n,t,!0):Bl(e,!!n,n?[]:"",!1));return;case"textarea":k=B=null;for(g in n)if(i=n[g],n.hasOwnProperty(g)&&i!=null&&!l.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:je(e,t,g,null,l,i)}for(h in l)if(i=l[h],c=n[h],l.hasOwnProperty(h)&&(i!=null||c!=null))switch(h){case"value":B=i;break;case"defaultValue":k=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==c&&je(e,t,h,i,l,c)}bf(e,B,k);return;case"option":for(var de in n)if(B=n[de],n.hasOwnProperty(de)&&B!=null&&!l.hasOwnProperty(de))switch(de){case"selected":e.selected=!1;break;default:je(e,t,de,null,l,B)}for(T in l)if(B=l[T],k=n[T],l.hasOwnProperty(T)&&B!==k&&(B!=null||k!=null))switch(T){case"selected":e.selected=B&&typeof B!="function"&&typeof B!="symbol";break;default:je(e,t,T,B,l,k)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ce in n)B=n[ce],n.hasOwnProperty(ce)&&B!=null&&!l.hasOwnProperty(ce)&&je(e,t,ce,null,l,B);for(H in l)if(B=l[H],k=n[H],l.hasOwnProperty(H)&&B!==k&&(B!=null||k!=null))switch(H){case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(o(137,t));break;default:je(e,t,H,B,l,k)}return;default:if(Fo(t)){for(var Ue in n)B=n[Ue],n.hasOwnProperty(Ue)&&B!==void 0&&!l.hasOwnProperty(Ue)&&Lc(e,t,Ue,void 0,l,B);for(V in l)B=l[V],k=n[V],!l.hasOwnProperty(V)||B===k||B===void 0&&k===void 0||Lc(e,t,V,B,l,k);return}}for(var j in n)B=n[j],n.hasOwnProperty(j)&&B!=null&&!l.hasOwnProperty(j)&&je(e,t,j,null,l,B);for(Q in l)B=l[Q],k=n[Q],!l.hasOwnProperty(Q)||B===k||B==null&&k==null||je(e,t,Q,B,l,k)}var Hc=null,Bc=null;function Fr(e){return e.nodeType===9?e:e.ownerDocument}function Sh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function wh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function kc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var qc=null;function E0(){var e=window.event;return e&&e.type==="popstate"?e===qc?!1:(qc=e,!0):(qc=null,!1)}var Eh=typeof setTimeout=="function"?setTimeout:void 0,T0=typeof clearTimeout=="function"?clearTimeout:void 0,Th=typeof Promise=="function"?Promise:void 0,A0=typeof queueMicrotask=="function"?queueMicrotask:typeof Th<"u"?function(e){return Th.resolve(null).then(e).catch(R0)}:Eh;function R0(e){setTimeout(function(){throw e})}function Zn(e){return e==="head"}function Ah(e,t){var n=t,l=0,i=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var h=e.ownerDocument;if(n&1&&Ti(h.documentElement),n&2&&Ti(h.body),n&4)for(n=h.head,Ti(n),h=n.firstChild;h;){var g=h.nextSibling,T=h.nodeName;h[ka]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=g}}if(i===0){e.removeChild(c),Di(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);Di(t)}function Yc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Yc(n),Zo(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function C0(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[ka])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Xt(e.nextSibling),e===null)break}return null}function N0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Xt(e.nextSibling),e===null))return null;return e}function Vc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function O0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Gc=null;function Rh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Ch(e,t,n){switch(t=Fr(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Ti(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Zo(e)}var Yt=new Map,Nh=new Set;function Ir(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var xn=Z.d;Z.d={f:M0,r:_0,D:D0,C:z0,L:j0,m:U0,X:H0,S:L0,M:B0};function M0(){var e=xn.f(),t=Xr();return e||t}function _0(e){var t=jl(e);t!==null&&t.tag===5&&t.type==="form"?Jd(t):xn.r(e)}var ha=typeof document>"u"?null:document;function Oh(e,t,n){var l=ha;if(l&&typeof t=="string"&&t){var i=jt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Nh.has(i)||(Nh.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),ut(t,"link",e),Fe(t),l.head.appendChild(t)))}}function D0(e){xn.D(e),Oh("dns-prefetch",e,null)}function z0(e,t){xn.C(e,t),Oh("preconnect",e,t)}function j0(e,t,n){xn.L(e,t,n);var l=ha;if(l&&e&&t){var i='link[rel="preload"][as="'+jt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+jt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+jt(n.imageSizes)+'"]')):i+='[href="'+jt(e)+'"]';var c=i;switch(t){case"style":c=pa(e);break;case"script":c=va(e)}Yt.has(c)||(e=b({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Yt.set(c,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Ai(c))||t==="script"&&l.querySelector(Ri(c))||(t=l.createElement("link"),ut(t,"link",e),Fe(t),l.head.appendChild(t)))}}function U0(e,t){xn.m(e,t);var n=ha;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+jt(l)+'"][href="'+jt(e)+'"]',c=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=va(e)}if(!Yt.has(c)&&(e=b({rel:"modulepreload",href:e},t),Yt.set(c,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ri(c)))return}l=n.createElement("link"),ut(l,"link",e),Fe(l),n.head.appendChild(l)}}}function L0(e,t,n){xn.S(e,t,n);var l=ha;if(l&&e){var i=Ul(l).hoistableStyles,c=pa(e);t=t||"default";var h=i.get(c);if(!h){var g={loading:0,preload:null};if(h=l.querySelector(Ai(c)))g.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Yt.get(c))&&Xc(e,n);var T=h=l.createElement("link");Fe(T),ut(T,"link",e),T._p=new Promise(function(H,V){T.onload=H,T.onerror=V}),T.addEventListener("load",function(){g.loading|=1}),T.addEventListener("error",function(){g.loading|=2}),g.loading|=4,eo(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:g},i.set(c,h)}}}function H0(e,t){xn.X(e,t);var n=ha;if(n&&e){var l=Ul(n).hoistableScripts,i=va(e),c=l.get(i);c||(c=n.querySelector(Ri(i)),c||(e=b({src:e,async:!0},t),(t=Yt.get(i))&&Qc(e,t),c=n.createElement("script"),Fe(c),ut(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function B0(e,t){xn.M(e,t);var n=ha;if(n&&e){var l=Ul(n).hoistableScripts,i=va(e),c=l.get(i);c||(c=n.querySelector(Ri(i)),c||(e=b({src:e,async:!0,type:"module"},t),(t=Yt.get(i))&&Qc(e,t),c=n.createElement("script"),Fe(c),ut(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Mh(e,t,n,l){var i=(i=re.current)?Ir(i):null;if(!i)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=pa(n.href),n=Ul(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=pa(n.href);var c=Ul(i).hoistableStyles,h=c.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,h),(c=i.querySelector(Ai(e)))&&!c._p&&(h.instance=c,h.state.loading=5),Yt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Yt.set(e,n),c||k0(i,e,n,h.state))),t&&l===null)throw Error(o(528,""));return h}if(t&&l!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=va(n),n=Ul(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function pa(e){return'href="'+jt(e)+'"'}function Ai(e){return'link[rel="stylesheet"]['+e+"]"}function _h(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function k0(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),ut(t,"link",n),Fe(t),e.head.appendChild(t))}function va(e){return'[src="'+jt(e)+'"]'}function Ri(e){return"script[async]"+e}function Dh(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+jt(n.href)+'"]');if(l)return t.instance=l,Fe(l),l;var i=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Fe(l),ut(l,"style",i),eo(l,n.precedence,e),t.instance=l;case"stylesheet":i=pa(n.href);var c=e.querySelector(Ai(i));if(c)return t.state.loading|=4,t.instance=c,Fe(c),c;l=_h(n),(i=Yt.get(i))&&Xc(l,i),c=(e.ownerDocument||e).createElement("link"),Fe(c);var h=c;return h._p=new Promise(function(g,T){h.onload=g,h.onerror=T}),ut(c,"link",l),t.state.loading|=4,eo(c,n.precedence,e),t.instance=c;case"script":return c=va(n.src),(i=e.querySelector(Ri(c)))?(t.instance=i,Fe(i),i):(l=n,(i=Yt.get(c))&&(l=b({},n),Qc(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),Fe(i),ut(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,eo(l,n.precedence,e));return t.instance}function eo(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,c=i,h=0;h<l.length;h++){var g=l[h];if(g.dataset.precedence===t)c=g;else if(c!==i)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Xc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var to=null;function zh(e,t,n){if(to===null){var l=new Map,i=to=new Map;i.set(n,l)}else i=to,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var c=n[i];if(!(c[ka]||c[st]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var h=c.getAttribute(t)||"";h=e+h;var g=l.get(h);g?g.push(c):l.set(h,[c])}}return l}function jh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function q0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Uh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ci=null;function Y0(){}function V0(e,t,n){if(Ci===null)throw Error(o(475));var l=Ci;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=pa(n.href),c=e.querySelector(Ai(i));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=no.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,Fe(c);return}c=e.ownerDocument||e,n=_h(n),(i=Yt.get(i))&&Xc(n,i),c=c.createElement("link"),Fe(c);var h=c;h._p=new Promise(function(g,T){h.onload=g,h.onerror=T}),ut(c,"link",n),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=no.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function G0(){if(Ci===null)throw Error(o(475));var e=Ci;return e.stylesheets&&e.count===0&&Zc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Zc(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function no(){if(this.count--,this.count===0){if(this.stylesheets)Zc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var lo=null;function Zc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,lo=new Map,t.forEach(X0,e),lo=null,no.call(e))}function X0(e,t){if(!(t.state.loading&4)){var n=lo.get(e);if(n)var l=n.get(null);else{n=new Map,lo.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var h=i[c];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),l=h)}l&&n.set(null,l)}i=t.instance,h=i.getAttribute("data-precedence"),c=n.get(h)||l,c===l&&n.set(null,i),n.set(h,i),this.count++,l=no.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),c?c.parentNode.insertBefore(i,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ni={$$typeof:D,Provider:null,Consumer:null,_currentValue:Y,_currentValue2:Y,_threadCount:0};function Q0(e,t,n,l,i,c,h,g){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Vo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vo(0),this.hiddenUpdates=Vo(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function Lh(e,t,n,l,i,c,h,g,T,H,V,Q){return e=new Q0(e,t,n,h,g,T,H,Q),t=1,c===!0&&(t|=24),c=At(3,null,null,t),e.current=c,c.stateNode=e,t=Cu(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:t},_u(c),e}function Hh(e){return e?(e=Kl,e):Kl}function Bh(e,t,n,l,i,c){i=Hh(i),l.context===null?l.context=i:l.pendingContext=i,l=zn(t),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=jn(e,l,t),n!==null&&(Mt(n,e,t),ai(n,e,t))}function kh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Kc(e,t){kh(e,t),(e=e.alternate)&&kh(e,t)}function qh(e){if(e.tag===13){var t=Zl(e,67108864);t!==null&&Mt(t,e,67108864),Kc(e,67108864)}}var ao=!0;function Z0(e,t,n,l){var i=L.T;L.T=null;var c=Z.p;try{Z.p=2,Jc(e,t,n,l)}finally{Z.p=c,L.T=i}}function K0(e,t,n,l){var i=L.T;L.T=null;var c=Z.p;try{Z.p=8,Jc(e,t,n,l)}finally{Z.p=c,L.T=i}}function Jc(e,t,n,l){if(ao){var i=$c(l);if(i===null)Uc(e,t,l,io,n),Vh(e,l);else if($0(i,e,t,n,l))l.stopPropagation();else if(Vh(e,l),t&4&&-1<J0.indexOf(e)){for(;i!==null;){var c=jl(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var h=cl(c.pendingLanes);if(h!==0){var g=c;for(g.pendingLanes|=2,g.entangledLanes|=2;h;){var T=1<<31-Et(h);g.entanglements[1]|=T,h&=~T}Ft(c),(_e&6)===0&&(Vr=ct()+500,Si(0))}}break;case 13:g=Zl(c,2),g!==null&&Mt(g,c,2),Xr(),Kc(c,2)}if(c=$c(l),c===null&&Uc(e,t,l,io,n),c===i)break;i=c}i!==null&&l.stopPropagation()}else Uc(e,t,l,null,n)}}function $c(e){return e=eu(e),Pc(e)}var io=null;function Pc(e){if(io=null,e=zl(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return io=e,null}function Yh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Yo()){case ul:return 2;case tf:return 8;case Wi:case jg:return 32;case nf:return 268435456;default:return 32}default:return 32}}var Wc=!1,Kn=null,Jn=null,$n=null,Oi=new Map,Mi=new Map,Pn=[],J0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Vh(e,t){switch(e){case"focusin":case"focusout":Kn=null;break;case"dragenter":case"dragleave":Jn=null;break;case"mouseover":case"mouseout":$n=null;break;case"pointerover":case"pointerout":Oi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mi.delete(t.pointerId)}}function _i(e,t,n,l,i,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[i]},t!==null&&(t=jl(t),t!==null&&qh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function $0(e,t,n,l,i){switch(t){case"focusin":return Kn=_i(Kn,e,t,n,l,i),!0;case"dragenter":return Jn=_i(Jn,e,t,n,l,i),!0;case"mouseover":return $n=_i($n,e,t,n,l,i),!0;case"pointerover":var c=i.pointerId;return Oi.set(c,_i(Oi.get(c)||null,e,t,n,l,i)),!0;case"gotpointercapture":return c=i.pointerId,Mi.set(c,_i(Mi.get(c)||null,e,t,n,l,i)),!0}return!1}function Gh(e){var t=zl(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Vg(e.priority,function(){if(n.tag===13){var l=Ot();l=Go(l);var i=Zl(n,l);i!==null&&Mt(i,n,l),Kc(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ro(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=$c(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Io=l,n.target.dispatchEvent(l),Io=null}else return t=jl(n),t!==null&&qh(t),e.blockedOn=n,!1;t.shift()}return!0}function Xh(e,t,n){ro(e)&&n.delete(t)}function P0(){Wc=!1,Kn!==null&&ro(Kn)&&(Kn=null),Jn!==null&&ro(Jn)&&(Jn=null),$n!==null&&ro($n)&&($n=null),Oi.forEach(Xh),Mi.forEach(Xh)}function oo(e,t){e.blockedOn===t&&(e.blockedOn=null,Wc||(Wc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,P0)))}var uo=null;function Qh(e){uo!==e&&(uo=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){uo===e&&(uo=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(Pc(l||n)===null)continue;break}var c=jl(n);c!==null&&(e.splice(t,3),t-=3,Pu(c,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Di(e){function t(T){return oo(T,e)}Kn!==null&&oo(Kn,e),Jn!==null&&oo(Jn,e),$n!==null&&oo($n,e),Oi.forEach(t),Mi.forEach(t);for(var n=0;n<Pn.length;n++){var l=Pn[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Pn.length&&(n=Pn[0],n.blockedOn===null);)Gh(n),n.blockedOn===null&&Pn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],c=n[l+1],h=i[vt]||null;if(typeof c=="function")h||Qh(n);else if(h){var g=null;if(c&&c.hasAttribute("formAction")){if(i=c,h=c[vt]||null)g=h.formAction;else if(Pc(i)!==null)continue}else g=h.action;typeof g=="function"?n[l+1]=g:(n.splice(l,3),l-=3),Qh(n)}}}function Fc(e){this._internalRoot=e}co.prototype.render=Fc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,l=Ot();Bh(n,l,e,t,null,null)},co.prototype.unmount=Fc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Bh(e.current,2,null,e,null,null),Xr(),t[Dl]=null}};function co(e){this._internalRoot=e}co.prototype.unstable_scheduleHydration=function(e){if(e){var t=uf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pn.length&&t!==0&&t<Pn[n].priority;n++);Pn.splice(n,0,e),n===0&&Gh(e)}};var Zh=r.version;if(Zh!=="19.1.0")throw Error(o(527,Zh,"19.1.0"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=v(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var W0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var so=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!so.isDisabled&&so.supportsFiber)try{La=so.inject(W0),wt=so}catch{}}return ji.createRoot=function(e,t){if(!s(e))throw Error(o(299));var n=!1,l="",i=um,c=cm,h=sm,g=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(g=t.unstable_transitionCallbacks)),t=Lh(e,1,!1,null,null,n,l,i,c,h,g,null),e[Dl]=t.current,jc(e),new Fc(t)},ji.hydrateRoot=function(e,t,n){if(!s(e))throw Error(o(299));var l=!1,i="",c=um,h=cm,g=sm,T=null,H=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(H=n.formState)),t=Lh(e,1,!0,t,n??null,l,i,c,h,g,T,H),t.context=Hh(null),n=t.current,l=Ot(),l=Go(l),i=zn(l),i.callback=null,jn(n,i,l),n=l,t.current.lanes=n,Ba(t,n),Ft(t),e[Dl]=t.current,jc(e),new co(t)},ji.version="19.1.0",ji}var np;function ob(){if(np)return ts.exports;np=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),ts.exports=rb(),ts.exports}var ub=ob(),Ui={},lp;function cb(){if(lp)return Ui;lp=1,Object.defineProperty(Ui,"__esModule",{value:!0}),Ui.parse=d,Ui.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,s=Object.prototype.toString,f=(()=>{const E=function(){};return E.prototype=Object.create(null),E})();function d(E,C){const A=new f,S=E.length;if(S<2)return A;const N=(C==null?void 0:C.decode)||b;let O=0;do{const M=E.indexOf("=",O);if(M===-1)break;const D=E.indexOf(";",O),z=D===-1?S:D;if(M>z){O=E.lastIndexOf(";",M-1)+1;continue}const q=p(E,O,M),P=v(E,M,q),J=E.slice(q,P);if(A[J]===void 0){let K=p(E,M+1,z),ee=v(E,z,K);const fe=N(E.slice(K,ee));A[J]=fe}O=z+1}while(O<S);return A}function p(E,C,A){do{const S=E.charCodeAt(C);if(S!==32&&S!==9)return C}while(++C<A);return A}function v(E,C,A){for(;C>A;){const S=E.charCodeAt(--C);if(S!==32&&S!==9)return C+1}return A}function m(E,C,A){const S=(A==null?void 0:A.encode)||encodeURIComponent;if(!a.test(E))throw new TypeError(`argument name is invalid: ${E}`);const N=S(C);if(!r.test(N))throw new TypeError(`argument val is invalid: ${C}`);let O=E+"="+N;if(!A)return O;if(A.maxAge!==void 0){if(!Number.isInteger(A.maxAge))throw new TypeError(`option maxAge is invalid: ${A.maxAge}`);O+="; Max-Age="+A.maxAge}if(A.domain){if(!u.test(A.domain))throw new TypeError(`option domain is invalid: ${A.domain}`);O+="; Domain="+A.domain}if(A.path){if(!o.test(A.path))throw new TypeError(`option path is invalid: ${A.path}`);O+="; Path="+A.path}if(A.expires){if(!w(A.expires)||!Number.isFinite(A.expires.valueOf()))throw new TypeError(`option expires is invalid: ${A.expires}`);O+="; Expires="+A.expires.toUTCString()}if(A.httpOnly&&(O+="; HttpOnly"),A.secure&&(O+="; Secure"),A.partitioned&&(O+="; Partitioned"),A.priority)switch(typeof A.priority=="string"?A.priority.toLowerCase():void 0){case"low":O+="; Priority=Low";break;case"medium":O+="; Priority=Medium";break;case"high":O+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${A.priority}`)}if(A.sameSite)switch(typeof A.sameSite=="string"?A.sameSite.toLowerCase():A.sameSite){case!0:case"strict":O+="; SameSite=Strict";break;case"lax":O+="; SameSite=Lax";break;case"none":O+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${A.sameSite}`)}return O}function b(E){if(E.indexOf("%")===-1)return E;try{return decodeURIComponent(E)}catch{return E}}function w(E){return s.call(E)==="[object Date]"}return Ui}cb();var ap="popstate";function sb(a={}){function r(o,s){let{pathname:f,search:d,hash:p}=o.location;return ys("",{pathname:f,search:d,hash:p},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function u(o,s){return typeof s=="string"?s:qi(s)}return db(r,u,null,a)}function qe(a,r){if(a===!1||a===null||typeof a>"u")throw new Error(r)}function tn(a,r){if(!a){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function fb(){return Math.random().toString(36).substring(2,10)}function ip(a,r){return{usr:a.state,key:a.key,idx:r}}function ys(a,r,u=null,o){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof r=="string"?Oa(r):r,state:u,key:r&&r.key||o||fb()}}function qi({pathname:a="/",search:r="",hash:u=""}){return r&&r!=="?"&&(a+=r.charAt(0)==="?"?r:"?"+r),u&&u!=="#"&&(a+=u.charAt(0)==="#"?u:"#"+u),a}function Oa(a){let r={};if(a){let u=a.indexOf("#");u>=0&&(r.hash=a.substring(u),a=a.substring(0,u));let o=a.indexOf("?");o>=0&&(r.search=a.substring(o),a=a.substring(0,o)),a&&(r.pathname=a)}return r}function db(a,r,u,o={}){let{window:s=document.defaultView,v5Compat:f=!1}=o,d=s.history,p="POP",v=null,m=b();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function b(){return(d.state||{idx:null}).idx}function w(){p="POP";let N=b(),O=N==null?null:N-m;m=N,v&&v({action:p,location:S.location,delta:O})}function E(N,O){p="PUSH";let M=ys(S.location,N,O);m=b()+1;let D=ip(M,m),z=S.createHref(M);try{d.pushState(D,"",z)}catch(q){if(q instanceof DOMException&&q.name==="DataCloneError")throw q;s.location.assign(z)}f&&v&&v({action:p,location:S.location,delta:1})}function C(N,O){p="REPLACE";let M=ys(S.location,N,O);m=b();let D=ip(M,m),z=S.createHref(M);d.replaceState(D,"",z),f&&v&&v({action:p,location:S.location,delta:0})}function A(N){return mb(N)}let S={get action(){return p},get location(){return a(s,d)},listen(N){if(v)throw new Error("A history only accepts one active listener");return s.addEventListener(ap,w),v=N,()=>{s.removeEventListener(ap,w),v=null}},createHref(N){return r(s,N)},createURL:A,encodeLocation(N){let O=A(N);return{pathname:O.pathname,search:O.search,hash:O.hash}},push:E,replace:C,go(N){return d.go(N)}};return S}function mb(a,r=!1){let u="http://localhost";typeof window<"u"&&(u=window.location.origin!=="null"?window.location.origin:window.location.href),qe(u,"No window.location.(origin|href) available to create URL");let o=typeof a=="string"?a:qi(a);return o=o.replace(/ $/,"%20"),!r&&o.startsWith("//")&&(o=u+o),new URL(o,u)}function Qp(a,r,u="/"){return hb(a,r,u,!1)}function hb(a,r,u,o){let s=typeof r=="string"?Oa(r):r,f=Tn(s.pathname||"/",u);if(f==null)return null;let d=Zp(a);pb(d);let p=null;for(let v=0;p==null&&v<d.length;++v){let m=Rb(f);p=Tb(d[v],m,o)}return p}function Zp(a,r=[],u=[],o=""){let s=(f,d,p)=>{let v={relativePath:p===void 0?f.path||"":p,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};v.relativePath.startsWith("/")&&(qe(v.relativePath.startsWith(o),`Absolute route path "${v.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(o.length));let m=wn([o,v.relativePath]),b=u.concat(v);f.children&&f.children.length>0&&(qe(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),Zp(f.children,r,b,m)),!(f.path==null&&!f.index)&&r.push({path:m,score:wb(m,f.index),routesMeta:b})};return a.forEach((f,d)=>{var p;if(f.path===""||!((p=f.path)!=null&&p.includes("?")))s(f,d);else for(let v of Kp(f.path))s(f,d,v)}),r}function Kp(a){let r=a.split("/");if(r.length===0)return[];let[u,...o]=r,s=u.endsWith("?"),f=u.replace(/\?$/,"");if(o.length===0)return s?[f,""]:[f];let d=Kp(o.join("/")),p=[];return p.push(...d.map(v=>v===""?f:[f,v].join("/"))),s&&p.push(...d),p.map(v=>a.startsWith("/")&&v===""?"/":v)}function pb(a){a.sort((r,u)=>r.score!==u.score?u.score-r.score:Eb(r.routesMeta.map(o=>o.childrenIndex),u.routesMeta.map(o=>o.childrenIndex)))}var vb=/^:[\w-]+$/,gb=3,yb=2,bb=1,xb=10,Sb=-2,rp=a=>a==="*";function wb(a,r){let u=a.split("/"),o=u.length;return u.some(rp)&&(o+=Sb),r&&(o+=yb),u.filter(s=>!rp(s)).reduce((s,f)=>s+(vb.test(f)?gb:f===""?bb:xb),o)}function Eb(a,r){return a.length===r.length&&a.slice(0,-1).every((o,s)=>o===r[s])?a[a.length-1]-r[r.length-1]:0}function Tb(a,r,u=!1){let{routesMeta:o}=a,s={},f="/",d=[];for(let p=0;p<o.length;++p){let v=o[p],m=p===o.length-1,b=f==="/"?r:r.slice(f.length)||"/",w=Ao({path:v.relativePath,caseSensitive:v.caseSensitive,end:m},b),E=v.route;if(!w&&m&&u&&!o[o.length-1].route.index&&(w=Ao({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},b)),!w)return null;Object.assign(s,w.params),d.push({params:s,pathname:wn([f,w.pathname]),pathnameBase:Mb(wn([f,w.pathnameBase])),route:E}),w.pathnameBase!=="/"&&(f=wn([f,w.pathnameBase]))}return d}function Ao(a,r){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[u,o]=Ab(a.path,a.caseSensitive,a.end),s=r.match(u);if(!s)return null;let f=s[0],d=f.replace(/(.)\/+$/,"$1"),p=s.slice(1);return{params:o.reduce((m,{paramName:b,isOptional:w},E)=>{if(b==="*"){let A=p[E]||"";d=f.slice(0,f.length-A.length).replace(/(.)\/+$/,"$1")}const C=p[E];return w&&!C?m[b]=void 0:m[b]=(C||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:a}}function Ab(a,r=!1,u=!0){tn(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let o=[],s="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,v)=>(o.push({paramName:p,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(o.push({paramName:"*"}),s+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?s+="\\/*$":a!==""&&a!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,r?void 0:"i"),o]}function Rb(a){try{return a.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return tn(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),a}}function Tn(a,r){if(r==="/")return a;if(!a.toLowerCase().startsWith(r.toLowerCase()))return null;let u=r.endsWith("/")?r.length-1:r.length,o=a.charAt(u);return o&&o!=="/"?null:a.slice(u)||"/"}function Cb(a,r="/"){let{pathname:u,search:o="",hash:s=""}=typeof a=="string"?Oa(a):a;return{pathname:u?u.startsWith("/")?u:Nb(u,r):r,search:_b(o),hash:Db(s)}}function Nb(a,r){let u=r.replace(/\/+$/,"").split("/");return a.split("/").forEach(s=>{s===".."?u.length>1&&u.pop():s!=="."&&u.push(s)}),u.length>1?u.join("/"):"/"}function is(a,r,u,o){return`Cannot include a '${a}' character in a manually specified \`to.${r}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${u}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Ob(a){return a.filter((r,u)=>u===0||r.route.path&&r.route.path.length>0)}function Jp(a){let r=Ob(a);return r.map((u,o)=>o===r.length-1?u.pathname:u.pathnameBase)}function $p(a,r,u,o=!1){let s;typeof a=="string"?s=Oa(a):(s={...a},qe(!s.pathname||!s.pathname.includes("?"),is("?","pathname","search",s)),qe(!s.pathname||!s.pathname.includes("#"),is("#","pathname","hash",s)),qe(!s.search||!s.search.includes("#"),is("#","search","hash",s)));let f=a===""||s.pathname==="",d=f?"/":s.pathname,p;if(d==null)p=u;else{let w=r.length-1;if(!o&&d.startsWith("..")){let E=d.split("/");for(;E[0]==="..";)E.shift(),w-=1;s.pathname=E.join("/")}p=w>=0?r[w]:"/"}let v=Cb(s,p),m=d&&d!=="/"&&d.endsWith("/"),b=(f||d===".")&&u.endsWith("/");return!v.pathname.endsWith("/")&&(m||b)&&(v.pathname+="/"),v}var wn=a=>a.join("/").replace(/\/\/+/g,"/"),Mb=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),_b=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,Db=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function zb(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Pp=["POST","PUT","PATCH","DELETE"];new Set(Pp);var jb=["GET",...Pp];new Set(jb);var Ma=y.createContext(null);Ma.displayName="DataRouter";var Do=y.createContext(null);Do.displayName="DataRouterState";var Wp=y.createContext({isTransitioning:!1});Wp.displayName="ViewTransition";var Ub=y.createContext(new Map);Ub.displayName="Fetchers";var Lb=y.createContext(null);Lb.displayName="Await";var ln=y.createContext(null);ln.displayName="Navigation";var Zi=y.createContext(null);Zi.displayName="Location";var Cn=y.createContext({outlet:null,matches:[],isDataRoute:!1});Cn.displayName="Route";var Us=y.createContext(null);Us.displayName="RouteError";function Hb(a,{relative:r}={}){qe(Ki(),"useHref() may be used only in the context of a <Router> component.");let{basename:u,navigator:o}=y.useContext(ln),{hash:s,pathname:f,search:d}=Ji(a,{relative:r}),p=f;return u!=="/"&&(p=f==="/"?u:wn([u,f])),o.createHref({pathname:p,search:d,hash:s})}function Ki(){return y.useContext(Zi)!=null}function ll(){return qe(Ki(),"useLocation() may be used only in the context of a <Router> component."),y.useContext(Zi).location}var Fp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Ip(a){y.useContext(ln).static||y.useLayoutEffect(a)}function Bb(){let{isDataRoute:a}=y.useContext(Cn);return a?Wb():kb()}function kb(){qe(Ki(),"useNavigate() may be used only in the context of a <Router> component.");let a=y.useContext(Ma),{basename:r,navigator:u}=y.useContext(ln),{matches:o}=y.useContext(Cn),{pathname:s}=ll(),f=JSON.stringify(Jp(o)),d=y.useRef(!1);return Ip(()=>{d.current=!0}),y.useCallback((v,m={})=>{if(tn(d.current,Fp),!d.current)return;if(typeof v=="number"){u.go(v);return}let b=$p(v,JSON.parse(f),s,m.relative==="path");a==null&&r!=="/"&&(b.pathname=b.pathname==="/"?r:wn([r,b.pathname])),(m.replace?u.replace:u.push)(b,m.state,m)},[r,u,f,s,a])}y.createContext(null);function Ji(a,{relative:r}={}){let{matches:u}=y.useContext(Cn),{pathname:o}=ll(),s=JSON.stringify(Jp(u));return y.useMemo(()=>$p(a,JSON.parse(s),o,r==="path"),[a,s,o,r])}function qb(a,r){return ev(a,r)}function ev(a,r,u,o){var O;qe(Ki(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=y.useContext(ln),{matches:f}=y.useContext(Cn),d=f[f.length-1],p=d?d.params:{},v=d?d.pathname:"/",m=d?d.pathnameBase:"/",b=d&&d.route;{let M=b&&b.path||"";tv(v,!b||M.endsWith("*")||M.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${v}" (under <Route path="${M}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${M}"> to <Route path="${M==="/"?"*":`${M}/*`}">.`)}let w=ll(),E;if(r){let M=typeof r=="string"?Oa(r):r;qe(m==="/"||((O=M.pathname)==null?void 0:O.startsWith(m)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${M.pathname}" was given in the \`location\` prop.`),E=M}else E=w;let C=E.pathname||"/",A=C;if(m!=="/"){let M=m.replace(/^\//,"").split("/");A="/"+C.replace(/^\//,"").split("/").slice(M.length).join("/")}let S=Qp(a,{pathname:A});tn(b||S!=null,`No routes matched location "${E.pathname}${E.search}${E.hash}" `),tn(S==null||S[S.length-1].route.element!==void 0||S[S.length-1].route.Component!==void 0||S[S.length-1].route.lazy!==void 0,`Matched leaf route at location "${E.pathname}${E.search}${E.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let N=Qb(S&&S.map(M=>Object.assign({},M,{params:Object.assign({},p,M.params),pathname:wn([m,s.encodeLocation?s.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?m:wn([m,s.encodeLocation?s.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),f,u,o);return r&&N?y.createElement(Zi.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...E},navigationType:"POP"}},N):N}function Yb(){let a=Pb(),r=zb(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),u=a instanceof Error?a.stack:null,o="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=y.createElement(y.Fragment,null,y.createElement("p",null,"💿 Hey developer 👋"),y.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",y.createElement("code",{style:f},"ErrorBoundary")," or"," ",y.createElement("code",{style:f},"errorElement")," prop on your route.")),y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},r),u?y.createElement("pre",{style:s},u):null,d)}var Vb=y.createElement(Yb,null),Gb=class extends y.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,r){return r.location!==a.location||r.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:r.error,location:r.location,revalidation:a.revalidation||r.revalidation}}componentDidCatch(a,r){console.error("React Router caught the following error during render",a,r)}render(){return this.state.error!==void 0?y.createElement(Cn.Provider,{value:this.props.routeContext},y.createElement(Us.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Xb({routeContext:a,match:r,children:u}){let o=y.useContext(Ma);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),y.createElement(Cn.Provider,{value:a},u)}function Qb(a,r=[],u=null,o=null){if(a==null){if(!u)return null;if(u.errors)a=u.matches;else if(r.length===0&&!u.initialized&&u.matches.length>0)a=u.matches;else return null}let s=a,f=u==null?void 0:u.errors;if(f!=null){let v=s.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);qe(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),s=s.slice(0,Math.min(s.length,v+1))}let d=!1,p=-1;if(u)for(let v=0;v<s.length;v++){let m=s[v];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(p=v),m.route.id){let{loaderData:b,errors:w}=u,E=m.route.loader&&!b.hasOwnProperty(m.route.id)&&(!w||w[m.route.id]===void 0);if(m.route.lazy||E){d=!0,p>=0?s=s.slice(0,p+1):s=[s[0]];break}}}return s.reduceRight((v,m,b)=>{let w,E=!1,C=null,A=null;u&&(w=f&&m.route.id?f[m.route.id]:void 0,C=m.route.errorElement||Vb,d&&(p<0&&b===0?(tv("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),E=!0,A=null):p===b&&(E=!0,A=m.route.hydrateFallbackElement||null)));let S=r.concat(s.slice(0,b+1)),N=()=>{let O;return w?O=C:E?O=A:m.route.Component?O=y.createElement(m.route.Component,null):m.route.element?O=m.route.element:O=v,y.createElement(Xb,{match:m,routeContext:{outlet:v,matches:S,isDataRoute:u!=null},children:O})};return u&&(m.route.ErrorBoundary||m.route.errorElement||b===0)?y.createElement(Gb,{location:u.location,revalidation:u.revalidation,component:C,error:w,children:N(),routeContext:{outlet:null,matches:S,isDataRoute:!0}}):N()},null)}function Ls(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Zb(a){let r=y.useContext(Ma);return qe(r,Ls(a)),r}function Kb(a){let r=y.useContext(Do);return qe(r,Ls(a)),r}function Jb(a){let r=y.useContext(Cn);return qe(r,Ls(a)),r}function Hs(a){let r=Jb(a),u=r.matches[r.matches.length-1];return qe(u.route.id,`${a} can only be used on routes that contain a unique "id"`),u.route.id}function $b(){return Hs("useRouteId")}function Pb(){var o;let a=y.useContext(Us),r=Kb("useRouteError"),u=Hs("useRouteError");return a!==void 0?a:(o=r.errors)==null?void 0:o[u]}function Wb(){let{router:a}=Zb("useNavigate"),r=Hs("useNavigate"),u=y.useRef(!1);return Ip(()=>{u.current=!0}),y.useCallback(async(s,f={})=>{tn(u.current,Fp),u.current&&(typeof s=="number"?a.navigate(s):await a.navigate(s,{fromRouteId:r,...f}))},[a,r])}var op={};function tv(a,r,u){!r&&!op[a]&&(op[a]=!0,tn(!1,u))}y.memo(Fb);function Fb({routes:a,future:r,state:u}){return ev(a,void 0,u,r)}function bs(a){qe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ib({basename:a="/",children:r=null,location:u,navigationType:o="POP",navigator:s,static:f=!1}){qe(!Ki(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),p=y.useMemo(()=>({basename:d,navigator:s,static:f,future:{}}),[d,s,f]);typeof u=="string"&&(u=Oa(u));let{pathname:v="/",search:m="",hash:b="",state:w=null,key:E="default"}=u,C=y.useMemo(()=>{let A=Tn(v,d);return A==null?null:{location:{pathname:A,search:m,hash:b,state:w,key:E},navigationType:o}},[d,v,m,b,w,E,o]);return tn(C!=null,`<Router basename="${d}"> is not able to match the URL "${v}${m}${b}" because it does not start with the basename, so the <Router> won't render anything.`),C==null?null:y.createElement(ln.Provider,{value:p},y.createElement(Zi.Provider,{children:r,value:C}))}function ex({children:a,location:r}){return qb(xs(a),r)}function xs(a,r=[]){let u=[];return y.Children.forEach(a,(o,s)=>{if(!y.isValidElement(o))return;let f=[...r,s];if(o.type===y.Fragment){u.push.apply(u,xs(o.props.children,f));return}qe(o.type===bs,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),qe(!o.props.index||!o.props.children,"An index route cannot have child routes.");let d={id:o.props.id||f.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(d.children=xs(o.props.children,f)),u.push(d)}),u}var xo="get",So="application/x-www-form-urlencoded";function zo(a){return a!=null&&typeof a.tagName=="string"}function tx(a){return zo(a)&&a.tagName.toLowerCase()==="button"}function nx(a){return zo(a)&&a.tagName.toLowerCase()==="form"}function lx(a){return zo(a)&&a.tagName.toLowerCase()==="input"}function ax(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function ix(a,r){return a.button===0&&(!r||r==="_self")&&!ax(a)}var fo=null;function rx(){if(fo===null)try{new FormData(document.createElement("form"),0),fo=!1}catch{fo=!0}return fo}var ox=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function rs(a){return a!=null&&!ox.has(a)?(tn(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${So}"`),null):a}function ux(a,r){let u,o,s,f,d;if(nx(a)){let p=a.getAttribute("action");o=p?Tn(p,r):null,u=a.getAttribute("method")||xo,s=rs(a.getAttribute("enctype"))||So,f=new FormData(a)}else if(tx(a)||lx(a)&&(a.type==="submit"||a.type==="image")){let p=a.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=a.getAttribute("formaction")||p.getAttribute("action");if(o=v?Tn(v,r):null,u=a.getAttribute("formmethod")||p.getAttribute("method")||xo,s=rs(a.getAttribute("formenctype"))||rs(p.getAttribute("enctype"))||So,f=new FormData(p,a),!rx()){let{name:m,type:b,value:w}=a;if(b==="image"){let E=m?`${m}.`:"";f.append(`${E}x`,"0"),f.append(`${E}y`,"0")}else m&&f.append(m,w)}}else{if(zo(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');u=xo,o=null,s=So,d=a}return f&&s==="text/plain"&&(d=f,f=void 0),{action:o,method:u.toLowerCase(),encType:s,formData:f,body:d}}function Bs(a,r){if(a===!1||a===null||typeof a>"u")throw new Error(r)}async function cx(a,r){if(a.id in r)return r[a.id];try{let u=await import(a.module);return r[a.id]=u,u}catch(u){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(u),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function sx(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function fx(a,r,u){let o=await Promise.all(a.map(async s=>{let f=r.routes[s.route.id];if(f){let d=await cx(f,u);return d.links?d.links():[]}return[]}));return px(o.flat(1).filter(sx).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function up(a,r,u,o,s,f){let d=(v,m)=>u[m]?v.route.id!==u[m].route.id:!0,p=(v,m)=>{var b;return u[m].pathname!==v.pathname||((b=u[m].route.path)==null?void 0:b.endsWith("*"))&&u[m].params["*"]!==v.params["*"]};return f==="assets"?r.filter((v,m)=>d(v,m)||p(v,m)):f==="data"?r.filter((v,m)=>{var w;let b=o.routes[v.route.id];if(!b||!b.hasLoader)return!1;if(d(v,m)||p(v,m))return!0;if(v.route.shouldRevalidate){let E=v.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((w=u[0])==null?void 0:w.params)||{},nextUrl:new URL(a,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof E=="boolean")return E}return!0}):[]}function dx(a,r,{includeHydrateFallback:u}={}){return mx(a.map(o=>{let s=r.routes[o.route.id];if(!s)return[];let f=[s.module];return s.clientActionModule&&(f=f.concat(s.clientActionModule)),s.clientLoaderModule&&(f=f.concat(s.clientLoaderModule)),u&&s.hydrateFallbackModule&&(f=f.concat(s.hydrateFallbackModule)),s.imports&&(f=f.concat(s.imports)),f}).flat(1))}function mx(a){return[...new Set(a)]}function hx(a){let r={},u=Object.keys(a).sort();for(let o of u)r[o]=a[o];return r}function px(a,r){let u=new Set;return new Set(r),a.reduce((o,s)=>{let f=JSON.stringify(hx(s));return u.has(f)||(u.add(f),o.push({key:f,link:s})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var vx=new Set([100,101,204,205]);function gx(a,r){let u=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return u.pathname==="/"?u.pathname="_root.data":r&&Tn(u.pathname,r)==="/"?u.pathname=`${r.replace(/\/$/,"")}/_root.data`:u.pathname=`${u.pathname.replace(/\/$/,"")}.data`,u}function nv(){let a=y.useContext(Ma);return Bs(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function yx(){let a=y.useContext(Do);return Bs(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var ks=y.createContext(void 0);ks.displayName="FrameworkContext";function lv(){let a=y.useContext(ks);return Bs(a,"You must render this element inside a <HydratedRouter> element"),a}function bx(a,r){let u=y.useContext(ks),[o,s]=y.useState(!1),[f,d]=y.useState(!1),{onFocus:p,onBlur:v,onMouseEnter:m,onMouseLeave:b,onTouchStart:w}=r,E=y.useRef(null);y.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let S=O=>{O.forEach(M=>{d(M.isIntersecting)})},N=new IntersectionObserver(S,{threshold:.5});return E.current&&N.observe(E.current),()=>{N.disconnect()}}},[a]),y.useEffect(()=>{if(o){let S=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(S)}}},[o]);let C=()=>{s(!0)},A=()=>{s(!1),d(!1)};return u?a!=="intent"?[f,E,{}]:[f,E,{onFocus:Li(p,C),onBlur:Li(v,A),onMouseEnter:Li(m,C),onMouseLeave:Li(b,A),onTouchStart:Li(w,C)}]:[!1,E,{}]}function Li(a,r){return u=>{a&&a(u),u.defaultPrevented||r(u)}}function xx({page:a,...r}){let{router:u}=nv(),o=y.useMemo(()=>Qp(u.routes,a,u.basename),[u.routes,a,u.basename]);return o?y.createElement(wx,{page:a,matches:o,...r}):null}function Sx(a){let{manifest:r,routeModules:u}=lv(),[o,s]=y.useState([]);return y.useEffect(()=>{let f=!1;return fx(a,r,u).then(d=>{f||s(d)}),()=>{f=!0}},[a,r,u]),o}function wx({page:a,matches:r,...u}){let o=ll(),{manifest:s,routeModules:f}=lv(),{basename:d}=nv(),{loaderData:p,matches:v}=yx(),m=y.useMemo(()=>up(a,r,v,s,o,"data"),[a,r,v,s,o]),b=y.useMemo(()=>up(a,r,v,s,o,"assets"),[a,r,v,s,o]),w=y.useMemo(()=>{if(a===o.pathname+o.search+o.hash)return[];let A=new Set,S=!1;if(r.forEach(O=>{var D;let M=s.routes[O.route.id];!M||!M.hasLoader||(!m.some(z=>z.route.id===O.route.id)&&O.route.id in p&&((D=f[O.route.id])!=null&&D.shouldRevalidate)||M.hasClientLoader?S=!0:A.add(O.route.id))}),A.size===0)return[];let N=gx(a,d);return S&&A.size>0&&N.searchParams.set("_routes",r.filter(O=>A.has(O.route.id)).map(O=>O.route.id).join(",")),[N.pathname+N.search]},[d,p,o,s,m,r,a,f]),E=y.useMemo(()=>dx(b,s),[b,s]),C=Sx(b);return y.createElement(y.Fragment,null,w.map(A=>y.createElement("link",{key:A,rel:"prefetch",as:"fetch",href:A,...u})),E.map(A=>y.createElement("link",{key:A,rel:"modulepreload",href:A,...u})),C.map(({key:A,link:S})=>y.createElement("link",{key:A,...S})))}function Ex(...a){return r=>{a.forEach(u=>{typeof u=="function"?u(r):u!=null&&(u.current=r)})}}var av=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{av&&(window.__reactRouterVersion="7.6.1")}catch{}function Tx({basename:a,children:r,window:u}){let o=y.useRef();o.current==null&&(o.current=sb({window:u,v5Compat:!0}));let s=o.current,[f,d]=y.useState({action:s.action,location:s.location}),p=y.useCallback(v=>{y.startTransition(()=>d(v))},[d]);return y.useLayoutEffect(()=>s.listen(p),[s,p]),y.createElement(Ib,{basename:a,children:r,location:f.location,navigationType:f.action,navigator:s})}var iv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ki=y.forwardRef(function({onClick:r,discover:u="render",prefetch:o="none",relative:s,reloadDocument:f,replace:d,state:p,target:v,to:m,preventScrollReset:b,viewTransition:w,...E},C){let{basename:A}=y.useContext(ln),S=typeof m=="string"&&iv.test(m),N,O=!1;if(typeof m=="string"&&S&&(N=m,av))try{let ee=new URL(window.location.href),fe=m.startsWith("//")?new URL(ee.protocol+m):new URL(m),pe=Tn(fe.pathname,A);fe.origin===ee.origin&&pe!=null?m=pe+fe.search+fe.hash:O=!0}catch{tn(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let M=Hb(m,{relative:s}),[D,z,q]=bx(o,E),P=Nx(m,{replace:d,state:p,target:v,preventScrollReset:b,relative:s,viewTransition:w});function J(ee){r&&r(ee),ee.defaultPrevented||P(ee)}let K=y.createElement("a",{...E,...q,href:N||M,onClick:O||f?r:J,ref:Ex(C,z),target:v,"data-discover":!S&&u==="render"?"true":void 0});return D&&!S?y.createElement(y.Fragment,null,K,y.createElement(xx,{page:M})):K});ki.displayName="Link";var Ax=y.forwardRef(function({"aria-current":r="page",caseSensitive:u=!1,className:o="",end:s=!1,style:f,to:d,viewTransition:p,children:v,...m},b){let w=Ji(d,{relative:m.relative}),E=ll(),C=y.useContext(Do),{navigator:A,basename:S}=y.useContext(ln),N=C!=null&&zx(w)&&p===!0,O=A.encodeLocation?A.encodeLocation(w).pathname:w.pathname,M=E.pathname,D=C&&C.navigation&&C.navigation.location?C.navigation.location.pathname:null;u||(M=M.toLowerCase(),D=D?D.toLowerCase():null,O=O.toLowerCase()),D&&S&&(D=Tn(D,S)||D);const z=O!=="/"&&O.endsWith("/")?O.length-1:O.length;let q=M===O||!s&&M.startsWith(O)&&M.charAt(z)==="/",P=D!=null&&(D===O||!s&&D.startsWith(O)&&D.charAt(O.length)==="/"),J={isActive:q,isPending:P,isTransitioning:N},K=q?r:void 0,ee;typeof o=="function"?ee=o(J):ee=[o,q?"active":null,P?"pending":null,N?"transitioning":null].filter(Boolean).join(" ");let fe=typeof f=="function"?f(J):f;return y.createElement(ki,{...m,"aria-current":K,className:ee,ref:b,style:fe,to:d,viewTransition:p},typeof v=="function"?v(J):v)});Ax.displayName="NavLink";var Rx=y.forwardRef(({discover:a="render",fetcherKey:r,navigate:u,reloadDocument:o,replace:s,state:f,method:d=xo,action:p,onSubmit:v,relative:m,preventScrollReset:b,viewTransition:w,...E},C)=>{let A=_x(),S=Dx(p,{relative:m}),N=d.toLowerCase()==="get"?"get":"post",O=typeof p=="string"&&iv.test(p),M=D=>{if(v&&v(D),D.defaultPrevented)return;D.preventDefault();let z=D.nativeEvent.submitter,q=(z==null?void 0:z.getAttribute("formmethod"))||d;A(z||D.currentTarget,{fetcherKey:r,method:q,navigate:u,replace:s,state:f,relative:m,preventScrollReset:b,viewTransition:w})};return y.createElement("form",{ref:C,method:N,action:S,onSubmit:o?v:M,...E,"data-discover":!O&&a==="render"?"true":void 0})});Rx.displayName="Form";function Cx(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function rv(a){let r=y.useContext(Ma);return qe(r,Cx(a)),r}function Nx(a,{target:r,replace:u,state:o,preventScrollReset:s,relative:f,viewTransition:d}={}){let p=Bb(),v=ll(),m=Ji(a,{relative:f});return y.useCallback(b=>{if(ix(b,r)){b.preventDefault();let w=u!==void 0?u:qi(v)===qi(m);p(a,{replace:w,state:o,preventScrollReset:s,relative:f,viewTransition:d})}},[v,p,m,u,o,r,a,s,f,d])}var Ox=0,Mx=()=>`__${String(++Ox)}__`;function _x(){let{router:a}=rv("useSubmit"),{basename:r}=y.useContext(ln),u=$b();return y.useCallback(async(o,s={})=>{let{action:f,method:d,encType:p,formData:v,body:m}=ux(o,r);if(s.navigate===!1){let b=s.fetcherKey||Mx();await a.fetch(b,u,s.action||f,{preventScrollReset:s.preventScrollReset,formData:v,body:m,formMethod:s.method||d,formEncType:s.encType||p,flushSync:s.flushSync})}else await a.navigate(s.action||f,{preventScrollReset:s.preventScrollReset,formData:v,body:m,formMethod:s.method||d,formEncType:s.encType||p,replace:s.replace,state:s.state,fromRouteId:u,flushSync:s.flushSync,viewTransition:s.viewTransition})},[a,r,u])}function Dx(a,{relative:r}={}){let{basename:u}=y.useContext(ln),o=y.useContext(Cn);qe(o,"useFormAction must be used inside a RouteContext");let[s]=o.matches.slice(-1),f={...Ji(a||".",{relative:r})},d=ll();if(a==null){f.search=d.search;let p=new URLSearchParams(f.search),v=p.getAll("index");if(v.some(b=>b==="")){p.delete("index"),v.filter(w=>w).forEach(w=>p.append("index",w));let b=p.toString();f.search=b?`?${b}`:""}}return(!a||a===".")&&s.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),u!=="/"&&(f.pathname=f.pathname==="/"?u:wn([u,f.pathname])),qi(f)}function zx(a,r={}){let u=y.useContext(Wp);qe(u!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=rv("useViewTransitionState"),s=Ji(a,{relative:r.relative});if(!u.isTransitioning)return!1;let f=Tn(u.currentLocation.pathname,o)||u.currentLocation.pathname,d=Tn(u.nextLocation.pathname,o)||u.nextLocation.pathname;return Ao(s.pathname,d)!=null||Ao(s.pathname,f)!=null}[...vx];var $i=Xp();const jx=Vp($i);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ux=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Lx=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,u,o)=>o?o.toUpperCase():u.toLowerCase()),cp=a=>{const r=Lx(a);return r.charAt(0).toUpperCase()+r.slice(1)},ov=(...a)=>a.filter((r,u,o)=>!!r&&r.trim()!==""&&o.indexOf(r)===u).join(" ").trim(),Hx=a=>{for(const r in a)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Bx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=y.forwardRef(({color:a="currentColor",size:r=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:s="",children:f,iconNode:d,...p},v)=>y.createElement("svg",{ref:v,...Bx,width:r,height:r,stroke:a,strokeWidth:o?Number(u)*24/Number(r):u,className:ov("lucide",s),...!f&&!Hx(p)&&{"aria-hidden":"true"},...p},[...d.map(([m,b])=>y.createElement(m,b)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=(a,r)=>{const u=y.forwardRef(({className:o,...s},f)=>y.createElement(kx,{ref:f,iconNode:r,className:ov(`lucide-${Ux(cp(a))}`,`lucide-${a}`,o),...s}));return u.displayName=cp(a),u};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qx=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Yx=mt("arrow-left",qx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vx=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Gx=mt("calendar",Vx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xx=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],uv=mt("check",Xx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qx=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],cv=mt("chevron-down",Qx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Kx=mt("chevron-right",Zx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],$x=mt("chevron-up",Jx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Px=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Yi=mt("download",Px);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Ea=mt("file-text",Wx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fx=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Ix=mt("funnel",Fx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eS=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],tS=mt("list",eS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nS=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],qs=mt("loader-circle",nS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]],Ro=mt("pen-tool",lS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aS=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],iS=mt("rotate-ccw",aS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],oS=mt("search",rS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],cS=mt("send",uS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sS=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],fS=mt("x",sS);function sp(a,r){if(typeof a=="function")return a(r);a!=null&&(a.current=r)}function dS(...a){return r=>{let u=!1;const o=a.map(s=>{const f=sp(s,r);return!u&&typeof f=="function"&&(u=!0),f});if(u)return()=>{for(let s=0;s<o.length;s++){const f=o[s];typeof f=="function"?f():sp(a[s],null)}}}}function lt(...a){return y.useCallback(dS(...a),a)}function Vi(a){const r=mS(a),u=y.forwardRef((o,s)=>{const{children:f,...d}=o,p=y.Children.toArray(f),v=p.find(pS);if(v){const m=v.props.children,b=p.map(w=>w===v?y.Children.count(m)>1?y.Children.only(null):y.isValidElement(m)?m.props.children:null:w);return x.jsx(r,{...d,ref:s,children:y.isValidElement(m)?y.cloneElement(m,void 0,b):null})}return x.jsx(r,{...d,ref:s,children:f})});return u.displayName=`${a}.Slot`,u}var sv=Vi("Slot");function mS(a){const r=y.forwardRef((u,o)=>{const{children:s,...f}=u,d=y.isValidElement(s)?gS(s):void 0,p=lt(d,o);if(y.isValidElement(s)){const v=vS(f,s.props);return s.type!==y.Fragment&&(v.ref=p),y.cloneElement(s,v)}return y.Children.count(s)>1?y.Children.only(null):null});return r.displayName=`${a}.SlotClone`,r}var hS=Symbol("radix.slottable");function pS(a){return y.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===hS}function vS(a,r){const u={...r};for(const o in r){const s=a[o],f=r[o];/^on[A-Z]/.test(o)?s&&f?u[o]=(...p)=>{const v=f(...p);return s(...p),v}:s&&(u[o]=s):o==="style"?u[o]={...s,...f}:o==="className"&&(u[o]=[s,f].filter(Boolean).join(" "))}return{...a,...u}}function gS(a){var o,s;let r=(o=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:o.get,u=r&&"isReactWarning"in r&&r.isReactWarning;return u?a.ref:(r=(s=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:s.get,u=r&&"isReactWarning"in r&&r.isReactWarning,u?a.props.ref:a.props.ref||a.ref)}function fv(a){var r,u,o="";if(typeof a=="string"||typeof a=="number")o+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(r=0;r<s;r++)a[r]&&(u=fv(a[r]))&&(o&&(o+=" "),o+=u)}else for(u in a)a[u]&&(o&&(o+=" "),o+=u);return o}function dv(){for(var a,r,u=0,o="",s=arguments.length;u<s;u++)(a=arguments[u])&&(r=fv(a))&&(o&&(o+=" "),o+=r);return o}const fp=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,dp=dv,Ys=(a,r)=>u=>{var o;if((r==null?void 0:r.variants)==null)return dp(a,u==null?void 0:u.class,u==null?void 0:u.className);const{variants:s,defaultVariants:f}=r,d=Object.keys(s).map(m=>{const b=u==null?void 0:u[m],w=f==null?void 0:f[m];if(b===null)return null;const E=fp(b)||fp(w);return s[m][E]}),p=u&&Object.entries(u).reduce((m,b)=>{let[w,E]=b;return E===void 0||(m[w]=E),m},{}),v=r==null||(o=r.compoundVariants)===null||o===void 0?void 0:o.reduce((m,b)=>{let{class:w,className:E,...C}=b;return Object.entries(C).every(A=>{let[S,N]=A;return Array.isArray(N)?N.includes({...f,...p}[S]):{...f,...p}[S]===N})?[...m,w,E]:m},[]);return dp(a,d,v,u==null?void 0:u.class,u==null?void 0:u.className)},Vs="-",yS=a=>{const r=xS(a),{conflictingClassGroups:u,conflictingClassGroupModifiers:o}=a;return{getClassGroupId:d=>{const p=d.split(Vs);return p[0]===""&&p.length!==1&&p.shift(),mv(p,r)||bS(d)},getConflictingClassGroupIds:(d,p)=>{const v=u[d]||[];return p&&o[d]?[...v,...o[d]]:v}}},mv=(a,r)=>{var d;if(a.length===0)return r.classGroupId;const u=a[0],o=r.nextPart.get(u),s=o?mv(a.slice(1),o):void 0;if(s)return s;if(r.validators.length===0)return;const f=a.join(Vs);return(d=r.validators.find(({validator:p})=>p(f)))==null?void 0:d.classGroupId},mp=/^\[(.+)\]$/,bS=a=>{if(mp.test(a)){const r=mp.exec(a)[1],u=r==null?void 0:r.substring(0,r.indexOf(":"));if(u)return"arbitrary.."+u}},xS=a=>{const{theme:r,classGroups:u}=a,o={nextPart:new Map,validators:[]};for(const s in u)Ss(u[s],o,s,r);return o},Ss=(a,r,u,o)=>{a.forEach(s=>{if(typeof s=="string"){const f=s===""?r:hp(r,s);f.classGroupId=u;return}if(typeof s=="function"){if(SS(s)){Ss(s(o),r,u,o);return}r.validators.push({validator:s,classGroupId:u});return}Object.entries(s).forEach(([f,d])=>{Ss(d,hp(r,f),u,o)})})},hp=(a,r)=>{let u=a;return r.split(Vs).forEach(o=>{u.nextPart.has(o)||u.nextPart.set(o,{nextPart:new Map,validators:[]}),u=u.nextPart.get(o)}),u},SS=a=>a.isThemeGetter,wS=a=>{if(a<1)return{get:()=>{},set:()=>{}};let r=0,u=new Map,o=new Map;const s=(f,d)=>{u.set(f,d),r++,r>a&&(r=0,o=u,u=new Map)};return{get(f){let d=u.get(f);if(d!==void 0)return d;if((d=o.get(f))!==void 0)return s(f,d),d},set(f,d){u.has(f)?u.set(f,d):s(f,d)}}},ws="!",Es=":",ES=Es.length,TS=a=>{const{prefix:r,experimentalParseClassName:u}=a;let o=s=>{const f=[];let d=0,p=0,v=0,m;for(let A=0;A<s.length;A++){let S=s[A];if(d===0&&p===0){if(S===Es){f.push(s.slice(v,A)),v=A+ES;continue}if(S==="/"){m=A;continue}}S==="["?d++:S==="]"?d--:S==="("?p++:S===")"&&p--}const b=f.length===0?s:s.substring(v),w=AS(b),E=w!==b,C=m&&m>v?m-v:void 0;return{modifiers:f,hasImportantModifier:E,baseClassName:w,maybePostfixModifierPosition:C}};if(r){const s=r+Es,f=o;o=d=>d.startsWith(s)?f(d.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(u){const s=o;o=f=>u({className:f,parseClassName:s})}return o},AS=a=>a.endsWith(ws)?a.substring(0,a.length-1):a.startsWith(ws)?a.substring(1):a,RS=a=>{const r=Object.fromEntries(a.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const s=[];let f=[];return o.forEach(d=>{d[0]==="["||r[d]?(s.push(...f.sort(),d),f=[]):f.push(d)}),s.push(...f.sort()),s}},CS=a=>({cache:wS(a.cacheSize),parseClassName:TS(a),sortModifiers:RS(a),...yS(a)}),NS=/\s+/,OS=(a,r)=>{const{parseClassName:u,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:f}=r,d=[],p=a.trim().split(NS);let v="";for(let m=p.length-1;m>=0;m-=1){const b=p[m],{isExternal:w,modifiers:E,hasImportantModifier:C,baseClassName:A,maybePostfixModifierPosition:S}=u(b);if(w){v=b+(v.length>0?" "+v:v);continue}let N=!!S,O=o(N?A.substring(0,S):A);if(!O){if(!N){v=b+(v.length>0?" "+v:v);continue}if(O=o(A),!O){v=b+(v.length>0?" "+v:v);continue}N=!1}const M=f(E).join(":"),D=C?M+ws:M,z=D+O;if(d.includes(z))continue;d.push(z);const q=s(O,N);for(let P=0;P<q.length;++P){const J=q[P];d.push(D+J)}v=b+(v.length>0?" "+v:v)}return v};function MS(){let a=0,r,u,o="";for(;a<arguments.length;)(r=arguments[a++])&&(u=hv(r))&&(o&&(o+=" "),o+=u);return o}const hv=a=>{if(typeof a=="string")return a;let r,u="";for(let o=0;o<a.length;o++)a[o]&&(r=hv(a[o]))&&(u&&(u+=" "),u+=r);return u};function _S(a,...r){let u,o,s,f=d;function d(v){const m=r.reduce((b,w)=>w(b),a());return u=CS(m),o=u.cache.get,s=u.cache.set,f=p,p(v)}function p(v){const m=o(v);if(m)return m;const b=OS(v,u);return s(v,b),b}return function(){return f(MS.apply(null,arguments))}}const We=a=>{const r=u=>u[a]||[];return r.isThemeGetter=!0,r},pv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,vv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,DS=/^\d+\/\d+$/,zS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,jS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,US=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,LS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,HS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ga=a=>DS.test(a),be=a=>!!a&&!Number.isNaN(Number(a)),Fn=a=>!!a&&Number.isInteger(Number(a)),os=a=>a.endsWith("%")&&be(a.slice(0,-1)),Sn=a=>zS.test(a),BS=()=>!0,kS=a=>jS.test(a)&&!US.test(a),gv=()=>!1,qS=a=>LS.test(a),YS=a=>HS.test(a),VS=a=>!ne(a)&&!le(a),GS=a=>_a(a,xv,gv),ne=a=>pv.test(a),Cl=a=>_a(a,Sv,kS),us=a=>_a(a,JS,be),pp=a=>_a(a,yv,gv),XS=a=>_a(a,bv,YS),mo=a=>_a(a,wv,qS),le=a=>vv.test(a),Hi=a=>Da(a,Sv),QS=a=>Da(a,$S),vp=a=>Da(a,yv),ZS=a=>Da(a,xv),KS=a=>Da(a,bv),ho=a=>Da(a,wv,!0),_a=(a,r,u)=>{const o=pv.exec(a);return o?o[1]?r(o[1]):u(o[2]):!1},Da=(a,r,u=!1)=>{const o=vv.exec(a);return o?o[1]?r(o[1]):u:!1},yv=a=>a==="position"||a==="percentage",bv=a=>a==="image"||a==="url",xv=a=>a==="length"||a==="size"||a==="bg-size",Sv=a=>a==="length",JS=a=>a==="number",$S=a=>a==="family-name",wv=a=>a==="shadow",PS=()=>{const a=We("color"),r=We("font"),u=We("text"),o=We("font-weight"),s=We("tracking"),f=We("leading"),d=We("breakpoint"),p=We("container"),v=We("spacing"),m=We("radius"),b=We("shadow"),w=We("inset-shadow"),E=We("text-shadow"),C=We("drop-shadow"),A=We("blur"),S=We("perspective"),N=We("aspect"),O=We("ease"),M=We("animate"),D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],q=()=>[...z(),le,ne],P=()=>["auto","hidden","clip","visible","scroll"],J=()=>["auto","contain","none"],K=()=>[le,ne,v],ee=()=>[ga,"full","auto",...K()],fe=()=>[Fn,"none","subgrid",le,ne],pe=()=>["auto",{span:["full",Fn,le,ne]},Fn,le,ne],me=()=>[Fn,"auto",le,ne],ge=()=>["auto","min","max","fr",le,ne],xe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ue=()=>["start","end","center","stretch","center-safe","end-safe"],L=()=>["auto",...K()],Z=()=>[ga,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],Y=()=>[a,le,ne],ae=()=>[...z(),vp,pp,{position:[le,ne]}],R=()=>["no-repeat",{repeat:["","x","y","space","round"]}],G=()=>["auto","cover","contain",ZS,GS,{size:[le,ne]}],W=()=>[os,Hi,Cl],$=()=>["","none","full",m,le,ne],I=()=>["",be,Hi,Cl],he=()=>["solid","dashed","dotted","double"],re=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>[be,os,vp,pp],oe=()=>["","none",A,le,ne],Me=()=>["none",be,le,ne],Re=()=>["none",be,le,ne],we=()=>[be,le,ne],Ee=()=>[ga,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Sn],breakpoint:[Sn],color:[BS],container:[Sn],"drop-shadow":[Sn],ease:["in","out","in-out"],font:[VS],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Sn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Sn],shadow:[Sn],spacing:["px",be],text:[Sn],"text-shadow":[Sn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ga,ne,le,N]}],container:["container"],columns:[{columns:[be,ne,le,p]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:q()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:J()}],"overscroll-x":[{"overscroll-x":J()}],"overscroll-y":[{"overscroll-y":J()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ee()}],"inset-x":[{"inset-x":ee()}],"inset-y":[{"inset-y":ee()}],start:[{start:ee()}],end:[{end:ee()}],top:[{top:ee()}],right:[{right:ee()}],bottom:[{bottom:ee()}],left:[{left:ee()}],visibility:["visible","invisible","collapse"],z:[{z:[Fn,"auto",le,ne]}],basis:[{basis:[ga,"full","auto",p,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[be,ga,"auto","initial","none",ne]}],grow:[{grow:["",be,le,ne]}],shrink:[{shrink:["",be,le,ne]}],order:[{order:[Fn,"first","last","none",le,ne]}],"grid-cols":[{"grid-cols":fe()}],"col-start-end":[{col:pe()}],"col-start":[{"col-start":me()}],"col-end":[{"col-end":me()}],"grid-rows":[{"grid-rows":fe()}],"row-start-end":[{row:pe()}],"row-start":[{"row-start":me()}],"row-end":[{"row-end":me()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ge()}],"auto-rows":[{"auto-rows":ge()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...xe(),"normal"]}],"justify-items":[{"justify-items":[...ue(),"normal"]}],"justify-self":[{"justify-self":["auto",...ue()]}],"align-content":[{content:["normal",...xe()]}],"align-items":[{items:[...ue(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ue(),{baseline:["","last"]}]}],"place-content":[{"place-content":xe()}],"place-items":[{"place-items":[...ue(),"baseline"]}],"place-self":[{"place-self":["auto",...ue()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:L()}],mx:[{mx:L()}],my:[{my:L()}],ms:[{ms:L()}],me:[{me:L()}],mt:[{mt:L()}],mr:[{mr:L()}],mb:[{mb:L()}],ml:[{ml:L()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[p,"screen",...Z()]}],"min-w":[{"min-w":[p,"screen","none",...Z()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[d]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",u,Hi,Cl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,le,us]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",os,ne]}],"font-family":[{font:[QS,ne,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,le,ne]}],"line-clamp":[{"line-clamp":[be,"none",le,us]}],leading:[{leading:[f,...K()]}],"list-image":[{"list-image":["none",le,ne]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",le,ne]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:Y()}],"text-color":[{text:Y()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...he(),"wavy"]}],"text-decoration-thickness":[{decoration:[be,"from-font","auto",le,Cl]}],"text-decoration-color":[{decoration:Y()}],"underline-offset":[{"underline-offset":[be,"auto",le,ne]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",le,ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",le,ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ae()}],"bg-repeat":[{bg:R()}],"bg-size":[{bg:G()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Fn,le,ne],radial:["",le,ne],conic:[Fn,le,ne]},KS,XS]}],"bg-color":[{bg:Y()}],"gradient-from-pos":[{from:W()}],"gradient-via-pos":[{via:W()}],"gradient-to-pos":[{to:W()}],"gradient-from":[{from:Y()}],"gradient-via":[{via:Y()}],"gradient-to":[{to:Y()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...he(),"hidden","none"]}],"divide-style":[{divide:[...he(),"hidden","none"]}],"border-color":[{border:Y()}],"border-color-x":[{"border-x":Y()}],"border-color-y":[{"border-y":Y()}],"border-color-s":[{"border-s":Y()}],"border-color-e":[{"border-e":Y()}],"border-color-t":[{"border-t":Y()}],"border-color-r":[{"border-r":Y()}],"border-color-b":[{"border-b":Y()}],"border-color-l":[{"border-l":Y()}],"divide-color":[{divide:Y()}],"outline-style":[{outline:[...he(),"none","hidden"]}],"outline-offset":[{"outline-offset":[be,le,ne]}],"outline-w":[{outline:["",be,Hi,Cl]}],"outline-color":[{outline:Y()}],shadow:[{shadow:["","none",b,ho,mo]}],"shadow-color":[{shadow:Y()}],"inset-shadow":[{"inset-shadow":["none",w,ho,mo]}],"inset-shadow-color":[{"inset-shadow":Y()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:Y()}],"ring-offset-w":[{"ring-offset":[be,Cl]}],"ring-offset-color":[{"ring-offset":Y()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":Y()}],"text-shadow":[{"text-shadow":["none",E,ho,mo]}],"text-shadow-color":[{"text-shadow":Y()}],opacity:[{opacity:[be,le,ne]}],"mix-blend":[{"mix-blend":[...re(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":re()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[be]}],"mask-image-linear-from-pos":[{"mask-linear-from":F()}],"mask-image-linear-to-pos":[{"mask-linear-to":F()}],"mask-image-linear-from-color":[{"mask-linear-from":Y()}],"mask-image-linear-to-color":[{"mask-linear-to":Y()}],"mask-image-t-from-pos":[{"mask-t-from":F()}],"mask-image-t-to-pos":[{"mask-t-to":F()}],"mask-image-t-from-color":[{"mask-t-from":Y()}],"mask-image-t-to-color":[{"mask-t-to":Y()}],"mask-image-r-from-pos":[{"mask-r-from":F()}],"mask-image-r-to-pos":[{"mask-r-to":F()}],"mask-image-r-from-color":[{"mask-r-from":Y()}],"mask-image-r-to-color":[{"mask-r-to":Y()}],"mask-image-b-from-pos":[{"mask-b-from":F()}],"mask-image-b-to-pos":[{"mask-b-to":F()}],"mask-image-b-from-color":[{"mask-b-from":Y()}],"mask-image-b-to-color":[{"mask-b-to":Y()}],"mask-image-l-from-pos":[{"mask-l-from":F()}],"mask-image-l-to-pos":[{"mask-l-to":F()}],"mask-image-l-from-color":[{"mask-l-from":Y()}],"mask-image-l-to-color":[{"mask-l-to":Y()}],"mask-image-x-from-pos":[{"mask-x-from":F()}],"mask-image-x-to-pos":[{"mask-x-to":F()}],"mask-image-x-from-color":[{"mask-x-from":Y()}],"mask-image-x-to-color":[{"mask-x-to":Y()}],"mask-image-y-from-pos":[{"mask-y-from":F()}],"mask-image-y-to-pos":[{"mask-y-to":F()}],"mask-image-y-from-color":[{"mask-y-from":Y()}],"mask-image-y-to-color":[{"mask-y-to":Y()}],"mask-image-radial":[{"mask-radial":[le,ne]}],"mask-image-radial-from-pos":[{"mask-radial-from":F()}],"mask-image-radial-to-pos":[{"mask-radial-to":F()}],"mask-image-radial-from-color":[{"mask-radial-from":Y()}],"mask-image-radial-to-color":[{"mask-radial-to":Y()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":z()}],"mask-image-conic-pos":[{"mask-conic":[be]}],"mask-image-conic-from-pos":[{"mask-conic-from":F()}],"mask-image-conic-to-pos":[{"mask-conic-to":F()}],"mask-image-conic-from-color":[{"mask-conic-from":Y()}],"mask-image-conic-to-color":[{"mask-conic-to":Y()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ae()}],"mask-repeat":[{mask:R()}],"mask-size":[{mask:G()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",le,ne]}],filter:[{filter:["","none",le,ne]}],blur:[{blur:oe()}],brightness:[{brightness:[be,le,ne]}],contrast:[{contrast:[be,le,ne]}],"drop-shadow":[{"drop-shadow":["","none",C,ho,mo]}],"drop-shadow-color":[{"drop-shadow":Y()}],grayscale:[{grayscale:["",be,le,ne]}],"hue-rotate":[{"hue-rotate":[be,le,ne]}],invert:[{invert:["",be,le,ne]}],saturate:[{saturate:[be,le,ne]}],sepia:[{sepia:["",be,le,ne]}],"backdrop-filter":[{"backdrop-filter":["","none",le,ne]}],"backdrop-blur":[{"backdrop-blur":oe()}],"backdrop-brightness":[{"backdrop-brightness":[be,le,ne]}],"backdrop-contrast":[{"backdrop-contrast":[be,le,ne]}],"backdrop-grayscale":[{"backdrop-grayscale":["",be,le,ne]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[be,le,ne]}],"backdrop-invert":[{"backdrop-invert":["",be,le,ne]}],"backdrop-opacity":[{"backdrop-opacity":[be,le,ne]}],"backdrop-saturate":[{"backdrop-saturate":[be,le,ne]}],"backdrop-sepia":[{"backdrop-sepia":["",be,le,ne]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",le,ne]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[be,"initial",le,ne]}],ease:[{ease:["linear","initial",O,le,ne]}],delay:[{delay:[be,le,ne]}],animate:[{animate:["none",M,le,ne]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[S,le,ne]}],"perspective-origin":[{"perspective-origin":q()}],rotate:[{rotate:Me()}],"rotate-x":[{"rotate-x":Me()}],"rotate-y":[{"rotate-y":Me()}],"rotate-z":[{"rotate-z":Me()}],scale:[{scale:Re()}],"scale-x":[{"scale-x":Re()}],"scale-y":[{"scale-y":Re()}],"scale-z":[{"scale-z":Re()}],"scale-3d":["scale-3d"],skew:[{skew:we()}],"skew-x":[{"skew-x":we()}],"skew-y":[{"skew-y":we()}],transform:[{transform:[le,ne,"","none","gpu","cpu"]}],"transform-origin":[{origin:q()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ee()}],"translate-x":[{"translate-x":Ee()}],"translate-y":[{"translate-y":Ee()}],"translate-z":[{"translate-z":Ee()}],"translate-none":["translate-none"],accent:[{accent:Y()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:Y()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",le,ne]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",le,ne]}],fill:[{fill:["none",...Y()]}],"stroke-w":[{stroke:[be,Hi,Cl,us]}],stroke:[{stroke:["none",...Y()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},WS=_S(PS);function at(...a){return WS(dv(a))}const FS=Ys("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function nt({className:a,variant:r,size:u,asChild:o=!1,...s}){const f=o?sv:"button";return x.jsx(f,{"data-slot":"button",className:at(FS({variant:r,size:u,className:a})),...s})}const IS=()=>{const a=ll();return x.jsx("header",{className:"bg-white shadow-sm border-b",children:x.jsx("div",{className:"container mx-auto px-4 py-4",children:x.jsxs("div",{className:"flex items-center justify-between",children:[x.jsxs(ki,{to:"/",className:"flex items-center space-x-2",children:[x.jsx(Ea,{className:"h-8 w-8 text-blue-600"}),x.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Report Generator"})]}),x.jsxs("nav",{className:"flex items-center space-x-4",children:[x.jsx(ki,{to:"/",children:x.jsxs(nt,{variant:a.pathname==="/"?"default":"ghost",className:"flex items-center space-x-2",children:[x.jsx(Ea,{className:"h-4 w-4"}),x.jsx("span",{children:"Create Report"})]})}),x.jsx(ki,{to:"/reports",children:x.jsxs(nt,{variant:a.pathname==="/reports"?"default":"ghost",className:"flex items-center space-x-2",children:[x.jsx(tS,{className:"h-4 w-4"}),x.jsx("span",{children:"View Reports"})]})})]})]})})})};function Ta({className:a,...r}){return x.jsx("div",{"data-slot":"card",className:at("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function Aa({className:a,...r}){return x.jsx("div",{"data-slot":"card-header",className:at("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function Ra({className:a,...r}){return x.jsx("div",{"data-slot":"card-title",className:at("leading-none font-semibold",a),...r})}function Gi({className:a,...r}){return x.jsx("div",{"data-slot":"card-description",className:at("text-muted-foreground text-sm",a),...r})}function Ca({className:a,...r}){return x.jsx("div",{"data-slot":"card-content",className:at("px-6",a),...r})}const e1=({onTemplateSelect:a})=>{const[r,u]=y.useState([]),[o,s]=y.useState(!0),[f,d]=y.useState(null);y.useEffect(()=>{p()},[]);const p=async()=>{try{s(!0);const m=await fetch("/api/templates");if(!m.ok)throw new Error("Failed to fetch templates");const b=await m.json();u(b)}catch(m){d(m.message)}finally{s(!1)}},v=async m=>{try{const b=await fetch(`/api/templates/${m.id}/placeholders`);if(!b.ok)throw new Error("Failed to fetch template placeholders");const w=await b.json();a(w)}catch(b){d(b.message)}};return o?x.jsxs("div",{className:"flex items-center justify-center py-12",children:[x.jsx(qs,{className:"h-8 w-8 animate-spin text-blue-600"}),x.jsx("span",{className:"ml-2 text-gray-600",children:"Loading templates..."})]}):f?x.jsxs("div",{className:"text-center py-12",children:[x.jsxs("div",{className:"text-red-600 mb-4",children:["Error: ",f]}),x.jsx(nt,{onClick:p,variant:"outline",children:"Try Again"})]}):x.jsxs("div",{className:"space-y-6",children:[x.jsxs("div",{className:"text-center",children:[x.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Select a Template"}),x.jsx("p",{className:"text-gray-600",children:"Choose from available report templates to get started"})]}),x.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:r.map(m=>x.jsxs(Ta,{className:"hover:shadow-lg transition-shadow cursor-pointer group",children:[x.jsx(Aa,{children:x.jsxs("div",{className:"flex items-center justify-between",children:[x.jsxs("div",{className:"flex items-center space-x-3",children:[x.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:x.jsx(Ea,{className:"h-6 w-6 text-blue-600"})}),x.jsxs("div",{children:[x.jsx(Ra,{className:"text-lg",children:m.name}),x.jsx(Gi,{children:"Professional template for generating reports"})]})]}),x.jsx(Kx,{className:"h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors"})]})}),x.jsx(Ca,{children:x.jsx(nt,{onClick:()=>v(m),className:"w-full",children:"Use This Template"})})]},m.id))}),r.length===0&&x.jsxs("div",{className:"text-center py-12",children:[x.jsx(Ea,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),x.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Templates Available"}),x.jsx("p",{className:"text-gray-600",children:"Please check back later or contact support."})]})]})};function Ev({className:a,type:r,...u}){return x.jsx("input",{type:r,"data-slot":"input",className:at("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...u})}var t1=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ge=t1.reduce((a,r)=>{const u=Vi(`Primitive.${r}`),o=y.forwardRef((s,f)=>{const{asChild:d,...p}=s,v=d?u:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(v,{...p,ref:f})});return o.displayName=`Primitive.${r}`,{...a,[r]:o}},{});function n1(a,r){a&&$i.flushSync(()=>a.dispatchEvent(r))}var l1="Label",Tv=y.forwardRef((a,r)=>x.jsx(Ge.label,{...a,ref:r,onMouseDown:u=>{var s;u.target.closest("button, input, select, textarea")||((s=a.onMouseDown)==null||s.call(a,u),!u.defaultPrevented&&u.detail>1&&u.preventDefault())}}));Tv.displayName=l1;var a1=Tv;function i1({className:a,...r}){return x.jsx(a1,{"data-slot":"label",className:at("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}function r1({className:a,...r}){return x.jsx("textarea",{"data-slot":"textarea",className:at("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...r})}const o1=Ys("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function gp({className:a,variant:r,...u}){return x.jsx("div",{"data-slot":"alert",role:"alert",className:at(o1({variant:r}),a),...u})}function yp({className:a,...r}){return x.jsx("div",{"data-slot":"alert-description",className:at("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...r})}const u1=({template:a,onFormSubmit:r,onCancel:u})=>{const[o,s]=y.useState({}),[f,d]=y.useState(!1),[p,v]=y.useState(null),[m,b]=y.useState(null),[w,E]=y.useState(null),C=(D,z)=>{s(q=>({...q,[D]:z}))},A=async D=>{D.preventDefault(),d(!0),v(null),b(null);try{const z=await fetch("/api/reports/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({template_id:a.template_id,form_data:o})});if(!z.ok)throw new Error("Failed to generate report");const q=await z.json();E(q),b("Report generated successfully!")}catch(z){v(z.message)}finally{d(!1)}},S=D=>{if(w){const z=w.download_urls[D];window.open(z,"_blank")}},N=D=>{const z=D.toLowerCase();return z.includes("date")?"date":z.includes("time")?"time":z.includes("email")?"email":z.includes("phone")||z.includes("mobile")?"tel":z.includes("number")||z.includes("kms")||z.includes("rate")?"number":"text"},O=D=>{const z=D.toLowerCase();return z.includes("history")||z.includes("investigation")||z.includes("conclusion")||z.includes("responsibility")||z.includes("object")},M=D=>D.replace(/([A-Z])/g," $1").replace(/^./,z=>z.toUpperCase()).trim();return w?x.jsxs(Ta,{className:"max-w-2xl mx-auto",children:[x.jsxs(Aa,{children:[x.jsx(Ra,{className:"text-green-600",children:"Report Generated Successfully!"}),x.jsx(Gi,{children:"Your report has been generated and is ready for download."})]}),x.jsxs(Ca,{className:"space-y-4",children:[x.jsxs("div",{className:"flex space-x-4",children:[x.jsxs(nt,{onClick:()=>S("docx"),className:"flex items-center space-x-2",children:[x.jsx(Yi,{className:"h-4 w-4"}),x.jsx("span",{children:"Download DOCX"})]}),x.jsxs(nt,{onClick:()=>S("pdf"),variant:"outline",className:"flex items-center space-x-2",children:[x.jsx(Yi,{className:"h-4 w-4"}),x.jsx("span",{children:"Download PDF"})]})]}),x.jsx("div",{className:"pt-4 border-t",children:x.jsx(nt,{onClick:()=>{E(null),r()},variant:"outline",className:"w-full",children:"Create Another Report"})})]})]}):x.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[x.jsxs("div",{className:"flex items-center space-x-4",children:[x.jsxs(nt,{onClick:u,variant:"outline",size:"sm",children:[x.jsx(Yx,{className:"h-4 w-4 mr-2"}),"Back to Templates"]}),x.jsxs("div",{children:[x.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:a.name}),x.jsx("p",{className:"text-gray-600",children:"Fill in the form to generate your report"})]})]}),p&&x.jsx(gp,{variant:"destructive",children:x.jsx(yp,{children:p})}),m&&x.jsx(gp,{children:x.jsx(yp,{children:m})}),x.jsxs(Ta,{children:[x.jsxs(Aa,{children:[x.jsx(Ra,{children:"Report Information"}),x.jsx(Gi,{children:"Please fill in all the required information for your report"})]}),x.jsx(Ca,{children:x.jsxs("form",{onSubmit:A,className:"space-y-6",children:[x.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:a.placeholders.map(D=>x.jsxs("div",{className:"space-y-2",children:[x.jsx(i1,{htmlFor:D,children:M(D)}),O(D)?x.jsx(r1,{id:D,value:o[D]||"",onChange:z=>C(D,z.target.value),placeholder:`Enter ${M(D).toLowerCase()}`,rows:3}):x.jsx(Ev,{id:D,type:N(D),value:o[D]||"",onChange:z=>C(D,z.target.value),placeholder:`Enter ${M(D).toLowerCase()}`})]},D))}),x.jsxs("div",{className:"flex space-x-4 pt-6 border-t",children:[x.jsxs(nt,{type:"submit",disabled:f,className:"flex items-center space-x-2",children:[f?x.jsx(qs,{className:"h-4 w-4 animate-spin"}):x.jsx(cS,{className:"h-4 w-4"}),x.jsx("span",{children:f?"Generating...":"Generate Report"})]}),x.jsx(nt,{type:"button",onClick:u,variant:"outline",children:"Cancel"})]})]})})]})]})};function bp(a,[r,u]){return Math.min(u,Math.max(r,a))}function Je(a,r,{checkForDefaultPrevented:u=!0}={}){return function(s){if(a==null||a(s),u===!1||!s.defaultPrevented)return r==null?void 0:r(s)}}function Gs(a,r=[]){let u=[];function o(f,d){const p=y.createContext(d),v=u.length;u=[...u,d];const m=w=>{var O;const{scope:E,children:C,...A}=w,S=((O=E==null?void 0:E[a])==null?void 0:O[v])||p,N=y.useMemo(()=>A,Object.values(A));return x.jsx(S.Provider,{value:N,children:C})};m.displayName=f+"Provider";function b(w,E){var S;const C=((S=E==null?void 0:E[a])==null?void 0:S[v])||p,A=y.useContext(C);if(A)return A;if(d!==void 0)return d;throw new Error(`\`${w}\` must be used within \`${f}\``)}return[m,b]}const s=()=>{const f=u.map(d=>y.createContext(d));return function(p){const v=(p==null?void 0:p[a])||f;return y.useMemo(()=>({[`__scope${a}`]:{...p,[a]:v}}),[p,v])}};return s.scopeName=a,[o,c1(s,...r)]}function c1(...a){const r=a[0];if(a.length===1)return r;const u=()=>{const o=a.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(f){const d=o.reduce((p,{useScope:v,scopeName:m})=>{const w=v(f)[`__scope${m}`];return{...p,...w}},{});return y.useMemo(()=>({[`__scope${r.scopeName}`]:d}),[d])}};return u.scopeName=r.scopeName,u}function s1(a){const r=a+"CollectionProvider",[u,o]=Gs(r),[s,f]=u(r,{collectionRef:{current:null},itemMap:new Map}),d=S=>{const{scope:N,children:O}=S,M=In.useRef(null),D=In.useRef(new Map).current;return x.jsx(s,{scope:N,itemMap:D,collectionRef:M,children:O})};d.displayName=r;const p=a+"CollectionSlot",v=Vi(p),m=In.forwardRef((S,N)=>{const{scope:O,children:M}=S,D=f(p,O),z=lt(N,D.collectionRef);return x.jsx(v,{ref:z,children:M})});m.displayName=p;const b=a+"CollectionItemSlot",w="data-radix-collection-item",E=Vi(b),C=In.forwardRef((S,N)=>{const{scope:O,children:M,...D}=S,z=In.useRef(null),q=lt(N,z),P=f(b,O);return In.useEffect(()=>(P.itemMap.set(z,{ref:z,...D}),()=>void P.itemMap.delete(z))),x.jsx(E,{[w]:"",ref:q,children:M})});C.displayName=b;function A(S){const N=f(a+"CollectionConsumer",S);return In.useCallback(()=>{const M=N.collectionRef.current;if(!M)return[];const D=Array.from(M.querySelectorAll(`[${w}]`));return Array.from(N.itemMap.values()).sort((P,J)=>D.indexOf(P.ref.current)-D.indexOf(J.ref.current))},[N.collectionRef,N.itemMap])}return[{Provider:d,Slot:m,ItemSlot:C},A,o]}var f1=y.createContext(void 0);function d1(a){const r=y.useContext(f1);return a||r||"ltr"}function Nl(a){const r=y.useRef(a);return y.useEffect(()=>{r.current=a}),y.useMemo(()=>(...u)=>{var o;return(o=r.current)==null?void 0:o.call(r,...u)},[])}function m1(a,r=globalThis==null?void 0:globalThis.document){const u=Nl(a);y.useEffect(()=>{const o=s=>{s.key==="Escape"&&u(s)};return r.addEventListener("keydown",o,{capture:!0}),()=>r.removeEventListener("keydown",o,{capture:!0})},[u,r])}var h1="DismissableLayer",Ts="dismissableLayer.update",p1="dismissableLayer.pointerDownOutside",v1="dismissableLayer.focusOutside",xp,Av=y.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Rv=y.forwardRef((a,r)=>{const{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:f,onInteractOutside:d,onDismiss:p,...v}=a,m=y.useContext(Av),[b,w]=y.useState(null),E=(b==null?void 0:b.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,C]=y.useState({}),A=lt(r,J=>w(J)),S=Array.from(m.layers),[N]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),O=S.indexOf(N),M=b?S.indexOf(b):-1,D=m.layersWithOutsidePointerEventsDisabled.size>0,z=M>=O,q=b1(J=>{const K=J.target,ee=[...m.branches].some(fe=>fe.contains(K));!z||ee||(s==null||s(J),d==null||d(J),J.defaultPrevented||p==null||p())},E),P=x1(J=>{const K=J.target;[...m.branches].some(fe=>fe.contains(K))||(f==null||f(J),d==null||d(J),J.defaultPrevented||p==null||p())},E);return m1(J=>{M===m.layers.size-1&&(o==null||o(J),!J.defaultPrevented&&p&&(J.preventDefault(),p()))},E),y.useEffect(()=>{if(b)return u&&(m.layersWithOutsidePointerEventsDisabled.size===0&&(xp=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(b)),m.layers.add(b),Sp(),()=>{u&&m.layersWithOutsidePointerEventsDisabled.size===1&&(E.body.style.pointerEvents=xp)}},[b,E,u,m]),y.useEffect(()=>()=>{b&&(m.layers.delete(b),m.layersWithOutsidePointerEventsDisabled.delete(b),Sp())},[b,m]),y.useEffect(()=>{const J=()=>C({});return document.addEventListener(Ts,J),()=>document.removeEventListener(Ts,J)},[]),x.jsx(Ge.div,{...v,ref:A,style:{pointerEvents:D?z?"auto":"none":void 0,...a.style},onFocusCapture:Je(a.onFocusCapture,P.onFocusCapture),onBlurCapture:Je(a.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Je(a.onPointerDownCapture,q.onPointerDownCapture)})});Rv.displayName=h1;var g1="DismissableLayerBranch",y1=y.forwardRef((a,r)=>{const u=y.useContext(Av),o=y.useRef(null),s=lt(r,o);return y.useEffect(()=>{const f=o.current;if(f)return u.branches.add(f),()=>{u.branches.delete(f)}},[u.branches]),x.jsx(Ge.div,{...a,ref:s})});y1.displayName=g1;function b1(a,r=globalThis==null?void 0:globalThis.document){const u=Nl(a),o=y.useRef(!1),s=y.useRef(()=>{});return y.useEffect(()=>{const f=p=>{if(p.target&&!o.current){let v=function(){Cv(p1,u,m,{discrete:!0})};const m={originalEvent:p};p.pointerType==="touch"?(r.removeEventListener("click",s.current),s.current=v,r.addEventListener("click",s.current,{once:!0})):v()}else r.removeEventListener("click",s.current);o.current=!1},d=window.setTimeout(()=>{r.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(d),r.removeEventListener("pointerdown",f),r.removeEventListener("click",s.current)}},[r,u]),{onPointerDownCapture:()=>o.current=!0}}function x1(a,r=globalThis==null?void 0:globalThis.document){const u=Nl(a),o=y.useRef(!1);return y.useEffect(()=>{const s=f=>{f.target&&!o.current&&Cv(v1,u,{originalEvent:f},{discrete:!1})};return r.addEventListener("focusin",s),()=>r.removeEventListener("focusin",s)},[r,u]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Sp(){const a=new CustomEvent(Ts);document.dispatchEvent(a)}function Cv(a,r,u,{discrete:o}){const s=u.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:u});r&&s.addEventListener(a,r,{once:!0}),o?n1(s,f):s.dispatchEvent(f)}var cs=0;function S1(){y.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??wp()),document.body.insertAdjacentElement("beforeend",a[1]??wp()),cs++,()=>{cs===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),cs--}},[])}function wp(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var ss="focusScope.autoFocusOnMount",fs="focusScope.autoFocusOnUnmount",Ep={bubbles:!1,cancelable:!0},w1="FocusScope",Nv=y.forwardRef((a,r)=>{const{loop:u=!1,trapped:o=!1,onMountAutoFocus:s,onUnmountAutoFocus:f,...d}=a,[p,v]=y.useState(null),m=Nl(s),b=Nl(f),w=y.useRef(null),E=lt(r,S=>v(S)),C=y.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;y.useEffect(()=>{if(o){let S=function(D){if(C.paused||!p)return;const z=D.target;p.contains(z)?w.current=z:el(w.current,{select:!0})},N=function(D){if(C.paused||!p)return;const z=D.relatedTarget;z!==null&&(p.contains(z)||el(w.current,{select:!0}))},O=function(D){if(document.activeElement===document.body)for(const q of D)q.removedNodes.length>0&&el(p)};document.addEventListener("focusin",S),document.addEventListener("focusout",N);const M=new MutationObserver(O);return p&&M.observe(p,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",S),document.removeEventListener("focusout",N),M.disconnect()}}},[o,p,C.paused]),y.useEffect(()=>{if(p){Ap.add(C);const S=document.activeElement;if(!p.contains(S)){const O=new CustomEvent(ss,Ep);p.addEventListener(ss,m),p.dispatchEvent(O),O.defaultPrevented||(E1(N1(Ov(p)),{select:!0}),document.activeElement===S&&el(p))}return()=>{p.removeEventListener(ss,m),setTimeout(()=>{const O=new CustomEvent(fs,Ep);p.addEventListener(fs,b),p.dispatchEvent(O),O.defaultPrevented||el(S??document.body,{select:!0}),p.removeEventListener(fs,b),Ap.remove(C)},0)}}},[p,m,b,C]);const A=y.useCallback(S=>{if(!u&&!o||C.paused)return;const N=S.key==="Tab"&&!S.altKey&&!S.ctrlKey&&!S.metaKey,O=document.activeElement;if(N&&O){const M=S.currentTarget,[D,z]=T1(M);D&&z?!S.shiftKey&&O===z?(S.preventDefault(),u&&el(D,{select:!0})):S.shiftKey&&O===D&&(S.preventDefault(),u&&el(z,{select:!0})):O===M&&S.preventDefault()}},[u,o,C.paused]);return x.jsx(Ge.div,{tabIndex:-1,...d,ref:E,onKeyDown:A})});Nv.displayName=w1;function E1(a,{select:r=!1}={}){const u=document.activeElement;for(const o of a)if(el(o,{select:r}),document.activeElement!==u)return}function T1(a){const r=Ov(a),u=Tp(r,a),o=Tp(r.reverse(),a);return[u,o]}function Ov(a){const r=[],u=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const s=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||s?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;u.nextNode();)r.push(u.currentNode);return r}function Tp(a,r){for(const u of a)if(!A1(u,{upTo:r}))return u}function A1(a,{upTo:r}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(r!==void 0&&a===r)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function R1(a){return a instanceof HTMLInputElement&&"select"in a}function el(a,{select:r=!1}={}){if(a&&a.focus){const u=document.activeElement;a.focus({preventScroll:!0}),a!==u&&R1(a)&&r&&a.select()}}var Ap=C1();function C1(){let a=[];return{add(r){const u=a[0];r!==u&&(u==null||u.pause()),a=Rp(a,r),a.unshift(r)},remove(r){var u;a=Rp(a,r),(u=a[0])==null||u.resume()}}}function Rp(a,r){const u=[...a],o=u.indexOf(r);return o!==-1&&u.splice(o,1),u}function N1(a){return a.filter(r=>r.tagName!=="A")}var St=globalThis!=null&&globalThis.document?y.useLayoutEffect:()=>{},O1=Gp[" useId ".trim().toString()]||(()=>{}),M1=0;function Xs(a){const[r,u]=y.useState(O1());return St(()=>{u(o=>o??String(M1++))},[a]),a||(r?`radix-${r}`:"")}const _1=["top","right","bottom","left"],tl=Math.min,_t=Math.max,Co=Math.round,po=Math.floor,en=a=>({x:a,y:a}),D1={left:"right",right:"left",bottom:"top",top:"bottom"},z1={start:"end",end:"start"};function As(a,r,u){return _t(a,tl(r,u))}function An(a,r){return typeof a=="function"?a(r):a}function Rn(a){return a.split("-")[0]}function za(a){return a.split("-")[1]}function Qs(a){return a==="x"?"y":"x"}function Zs(a){return a==="y"?"height":"width"}function En(a){return["top","bottom"].includes(Rn(a))?"y":"x"}function Ks(a){return Qs(En(a))}function j1(a,r,u){u===void 0&&(u=!1);const o=za(a),s=Ks(a),f=Zs(s);let d=s==="x"?o===(u?"end":"start")?"right":"left":o==="start"?"bottom":"top";return r.reference[f]>r.floating[f]&&(d=No(d)),[d,No(d)]}function U1(a){const r=No(a);return[Rs(a),r,Rs(r)]}function Rs(a){return a.replace(/start|end/g,r=>z1[r])}function L1(a,r,u){const o=["left","right"],s=["right","left"],f=["top","bottom"],d=["bottom","top"];switch(a){case"top":case"bottom":return u?r?s:o:r?o:s;case"left":case"right":return r?f:d;default:return[]}}function H1(a,r,u,o){const s=za(a);let f=L1(Rn(a),u==="start",o);return s&&(f=f.map(d=>d+"-"+s),r&&(f=f.concat(f.map(Rs)))),f}function No(a){return a.replace(/left|right|bottom|top/g,r=>D1[r])}function B1(a){return{top:0,right:0,bottom:0,left:0,...a}}function Mv(a){return typeof a!="number"?B1(a):{top:a,right:a,bottom:a,left:a}}function Oo(a){const{x:r,y:u,width:o,height:s}=a;return{width:o,height:s,top:u,left:r,right:r+o,bottom:u+s,x:r,y:u}}function Cp(a,r,u){let{reference:o,floating:s}=a;const f=En(r),d=Ks(r),p=Zs(d),v=Rn(r),m=f==="y",b=o.x+o.width/2-s.width/2,w=o.y+o.height/2-s.height/2,E=o[p]/2-s[p]/2;let C;switch(v){case"top":C={x:b,y:o.y-s.height};break;case"bottom":C={x:b,y:o.y+o.height};break;case"right":C={x:o.x+o.width,y:w};break;case"left":C={x:o.x-s.width,y:w};break;default:C={x:o.x,y:o.y}}switch(za(r)){case"start":C[d]-=E*(u&&m?-1:1);break;case"end":C[d]+=E*(u&&m?-1:1);break}return C}const k1=async(a,r,u)=>{const{placement:o="bottom",strategy:s="absolute",middleware:f=[],platform:d}=u,p=f.filter(Boolean),v=await(d.isRTL==null?void 0:d.isRTL(r));let m=await d.getElementRects({reference:a,floating:r,strategy:s}),{x:b,y:w}=Cp(m,o,v),E=o,C={},A=0;for(let S=0;S<p.length;S++){const{name:N,fn:O}=p[S],{x:M,y:D,data:z,reset:q}=await O({x:b,y:w,initialPlacement:o,placement:E,strategy:s,middlewareData:C,rects:m,platform:d,elements:{reference:a,floating:r}});b=M??b,w=D??w,C={...C,[N]:{...C[N],...z}},q&&A<=50&&(A++,typeof q=="object"&&(q.placement&&(E=q.placement),q.rects&&(m=q.rects===!0?await d.getElementRects({reference:a,floating:r,strategy:s}):q.rects),{x:b,y:w}=Cp(m,E,v)),S=-1)}return{x:b,y:w,placement:E,strategy:s,middlewareData:C}};async function Xi(a,r){var u;r===void 0&&(r={});const{x:o,y:s,platform:f,rects:d,elements:p,strategy:v}=a,{boundary:m="clippingAncestors",rootBoundary:b="viewport",elementContext:w="floating",altBoundary:E=!1,padding:C=0}=An(r,a),A=Mv(C),N=p[E?w==="floating"?"reference":"floating":w],O=Oo(await f.getClippingRect({element:(u=await(f.isElement==null?void 0:f.isElement(N)))==null||u?N:N.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(p.floating)),boundary:m,rootBoundary:b,strategy:v})),M=w==="floating"?{x:o,y:s,width:d.floating.width,height:d.floating.height}:d.reference,D=await(f.getOffsetParent==null?void 0:f.getOffsetParent(p.floating)),z=await(f.isElement==null?void 0:f.isElement(D))?await(f.getScale==null?void 0:f.getScale(D))||{x:1,y:1}:{x:1,y:1},q=Oo(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:p,rect:M,offsetParent:D,strategy:v}):M);return{top:(O.top-q.top+A.top)/z.y,bottom:(q.bottom-O.bottom+A.bottom)/z.y,left:(O.left-q.left+A.left)/z.x,right:(q.right-O.right+A.right)/z.x}}const q1=a=>({name:"arrow",options:a,async fn(r){const{x:u,y:o,placement:s,rects:f,platform:d,elements:p,middlewareData:v}=r,{element:m,padding:b=0}=An(a,r)||{};if(m==null)return{};const w=Mv(b),E={x:u,y:o},C=Ks(s),A=Zs(C),S=await d.getDimensions(m),N=C==="y",O=N?"top":"left",M=N?"bottom":"right",D=N?"clientHeight":"clientWidth",z=f.reference[A]+f.reference[C]-E[C]-f.floating[A],q=E[C]-f.reference[C],P=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let J=P?P[D]:0;(!J||!await(d.isElement==null?void 0:d.isElement(P)))&&(J=p.floating[D]||f.floating[A]);const K=z/2-q/2,ee=J/2-S[A]/2-1,fe=tl(w[O],ee),pe=tl(w[M],ee),me=fe,ge=J-S[A]-pe,xe=J/2-S[A]/2+K,ue=As(me,xe,ge),L=!v.arrow&&za(s)!=null&&xe!==ue&&f.reference[A]/2-(xe<me?fe:pe)-S[A]/2<0,Z=L?xe<me?xe-me:xe-ge:0;return{[C]:E[C]+Z,data:{[C]:ue,centerOffset:xe-ue-Z,...L&&{alignmentOffset:Z}},reset:L}}}),Y1=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(r){var u,o;const{placement:s,middlewareData:f,rects:d,initialPlacement:p,platform:v,elements:m}=r,{mainAxis:b=!0,crossAxis:w=!0,fallbackPlacements:E,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:S=!0,...N}=An(a,r);if((u=f.arrow)!=null&&u.alignmentOffset)return{};const O=Rn(s),M=En(p),D=Rn(p)===p,z=await(v.isRTL==null?void 0:v.isRTL(m.floating)),q=E||(D||!S?[No(p)]:U1(p)),P=A!=="none";!E&&P&&q.push(...H1(p,S,A,z));const J=[p,...q],K=await Xi(r,N),ee=[];let fe=((o=f.flip)==null?void 0:o.overflows)||[];if(b&&ee.push(K[O]),w){const ue=j1(s,d,z);ee.push(K[ue[0]],K[ue[1]])}if(fe=[...fe,{placement:s,overflows:ee}],!ee.every(ue=>ue<=0)){var pe,me;const ue=(((pe=f.flip)==null?void 0:pe.index)||0)+1,L=J[ue];if(L){var ge;const Y=w==="alignment"?M!==En(L):!1,ae=((ge=fe[0])==null?void 0:ge.overflows[0])>0;if(!Y||ae)return{data:{index:ue,overflows:fe},reset:{placement:L}}}let Z=(me=fe.filter(Y=>Y.overflows[0]<=0).sort((Y,ae)=>Y.overflows[1]-ae.overflows[1])[0])==null?void 0:me.placement;if(!Z)switch(C){case"bestFit":{var xe;const Y=(xe=fe.filter(ae=>{if(P){const R=En(ae.placement);return R===M||R==="y"}return!0}).map(ae=>[ae.placement,ae.overflows.filter(R=>R>0).reduce((R,G)=>R+G,0)]).sort((ae,R)=>ae[1]-R[1])[0])==null?void 0:xe[0];Y&&(Z=Y);break}case"initialPlacement":Z=p;break}if(s!==Z)return{reset:{placement:Z}}}return{}}}};function Np(a,r){return{top:a.top-r.height,right:a.right-r.width,bottom:a.bottom-r.height,left:a.left-r.width}}function Op(a){return _1.some(r=>a[r]>=0)}const V1=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(r){const{rects:u}=r,{strategy:o="referenceHidden",...s}=An(a,r);switch(o){case"referenceHidden":{const f=await Xi(r,{...s,elementContext:"reference"}),d=Np(f,u.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:Op(d)}}}case"escaped":{const f=await Xi(r,{...s,altBoundary:!0}),d=Np(f,u.floating);return{data:{escapedOffsets:d,escaped:Op(d)}}}default:return{}}}}};async function G1(a,r){const{placement:u,platform:o,elements:s}=a,f=await(o.isRTL==null?void 0:o.isRTL(s.floating)),d=Rn(u),p=za(u),v=En(u)==="y",m=["left","top"].includes(d)?-1:1,b=f&&v?-1:1,w=An(r,a);let{mainAxis:E,crossAxis:C,alignmentAxis:A}=typeof w=="number"?{mainAxis:w,crossAxis:0,alignmentAxis:null}:{mainAxis:w.mainAxis||0,crossAxis:w.crossAxis||0,alignmentAxis:w.alignmentAxis};return p&&typeof A=="number"&&(C=p==="end"?A*-1:A),v?{x:C*b,y:E*m}:{x:E*m,y:C*b}}const X1=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(r){var u,o;const{x:s,y:f,placement:d,middlewareData:p}=r,v=await G1(r,a);return d===((u=p.offset)==null?void 0:u.placement)&&(o=p.arrow)!=null&&o.alignmentOffset?{}:{x:s+v.x,y:f+v.y,data:{...v,placement:d}}}}},Q1=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(r){const{x:u,y:o,placement:s}=r,{mainAxis:f=!0,crossAxis:d=!1,limiter:p={fn:N=>{let{x:O,y:M}=N;return{x:O,y:M}}},...v}=An(a,r),m={x:u,y:o},b=await Xi(r,v),w=En(Rn(s)),E=Qs(w);let C=m[E],A=m[w];if(f){const N=E==="y"?"top":"left",O=E==="y"?"bottom":"right",M=C+b[N],D=C-b[O];C=As(M,C,D)}if(d){const N=w==="y"?"top":"left",O=w==="y"?"bottom":"right",M=A+b[N],D=A-b[O];A=As(M,A,D)}const S=p.fn({...r,[E]:C,[w]:A});return{...S,data:{x:S.x-u,y:S.y-o,enabled:{[E]:f,[w]:d}}}}}},Z1=function(a){return a===void 0&&(a={}),{options:a,fn(r){const{x:u,y:o,placement:s,rects:f,middlewareData:d}=r,{offset:p=0,mainAxis:v=!0,crossAxis:m=!0}=An(a,r),b={x:u,y:o},w=En(s),E=Qs(w);let C=b[E],A=b[w];const S=An(p,r),N=typeof S=="number"?{mainAxis:S,crossAxis:0}:{mainAxis:0,crossAxis:0,...S};if(v){const D=E==="y"?"height":"width",z=f.reference[E]-f.floating[D]+N.mainAxis,q=f.reference[E]+f.reference[D]-N.mainAxis;C<z?C=z:C>q&&(C=q)}if(m){var O,M;const D=E==="y"?"width":"height",z=["top","left"].includes(Rn(s)),q=f.reference[w]-f.floating[D]+(z&&((O=d.offset)==null?void 0:O[w])||0)+(z?0:N.crossAxis),P=f.reference[w]+f.reference[D]+(z?0:((M=d.offset)==null?void 0:M[w])||0)-(z?N.crossAxis:0);A<q?A=q:A>P&&(A=P)}return{[E]:C,[w]:A}}}},K1=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(r){var u,o;const{placement:s,rects:f,platform:d,elements:p}=r,{apply:v=()=>{},...m}=An(a,r),b=await Xi(r,m),w=Rn(s),E=za(s),C=En(s)==="y",{width:A,height:S}=f.floating;let N,O;w==="top"||w==="bottom"?(N=w,O=E===(await(d.isRTL==null?void 0:d.isRTL(p.floating))?"start":"end")?"left":"right"):(O=w,N=E==="end"?"top":"bottom");const M=S-b.top-b.bottom,D=A-b.left-b.right,z=tl(S-b[N],M),q=tl(A-b[O],D),P=!r.middlewareData.shift;let J=z,K=q;if((u=r.middlewareData.shift)!=null&&u.enabled.x&&(K=D),(o=r.middlewareData.shift)!=null&&o.enabled.y&&(J=M),P&&!E){const fe=_t(b.left,0),pe=_t(b.right,0),me=_t(b.top,0),ge=_t(b.bottom,0);C?K=A-2*(fe!==0||pe!==0?fe+pe:_t(b.left,b.right)):J=S-2*(me!==0||ge!==0?me+ge:_t(b.top,b.bottom))}await v({...r,availableWidth:K,availableHeight:J});const ee=await d.getDimensions(p.floating);return A!==ee.width||S!==ee.height?{reset:{rects:!0}}:{}}}};function jo(){return typeof window<"u"}function ja(a){return _v(a)?(a.nodeName||"").toLowerCase():"#document"}function Dt(a){var r;return(a==null||(r=a.ownerDocument)==null?void 0:r.defaultView)||window}function an(a){var r;return(r=(_v(a)?a.ownerDocument:a.document)||window.document)==null?void 0:r.documentElement}function _v(a){return jo()?a instanceof Node||a instanceof Dt(a).Node:!1}function Zt(a){return jo()?a instanceof Element||a instanceof Dt(a).Element:!1}function nn(a){return jo()?a instanceof HTMLElement||a instanceof Dt(a).HTMLElement:!1}function Mp(a){return!jo()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof Dt(a).ShadowRoot}function Pi(a){const{overflow:r,overflowX:u,overflowY:o,display:s}=Kt(a);return/auto|scroll|overlay|hidden|clip/.test(r+o+u)&&!["inline","contents"].includes(s)}function J1(a){return["table","td","th"].includes(ja(a))}function Uo(a){return[":popover-open",":modal"].some(r=>{try{return a.matches(r)}catch{return!1}})}function Js(a){const r=$s(),u=Zt(a)?Kt(a):a;return["transform","translate","scale","rotate","perspective"].some(o=>u[o]?u[o]!=="none":!1)||(u.containerType?u.containerType!=="normal":!1)||!r&&(u.backdropFilter?u.backdropFilter!=="none":!1)||!r&&(u.filter?u.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(u.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(u.contain||"").includes(o))}function $1(a){let r=nl(a);for(;nn(r)&&!Na(r);){if(Js(r))return r;if(Uo(r))return null;r=nl(r)}return null}function $s(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Na(a){return["html","body","#document"].includes(ja(a))}function Kt(a){return Dt(a).getComputedStyle(a)}function Lo(a){return Zt(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function nl(a){if(ja(a)==="html")return a;const r=a.assignedSlot||a.parentNode||Mp(a)&&a.host||an(a);return Mp(r)?r.host:r}function Dv(a){const r=nl(a);return Na(r)?a.ownerDocument?a.ownerDocument.body:a.body:nn(r)&&Pi(r)?r:Dv(r)}function Qi(a,r,u){var o;r===void 0&&(r=[]),u===void 0&&(u=!0);const s=Dv(a),f=s===((o=a.ownerDocument)==null?void 0:o.body),d=Dt(s);if(f){const p=Cs(d);return r.concat(d,d.visualViewport||[],Pi(s)?s:[],p&&u?Qi(p):[])}return r.concat(s,Qi(s,[],u))}function Cs(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function zv(a){const r=Kt(a);let u=parseFloat(r.width)||0,o=parseFloat(r.height)||0;const s=nn(a),f=s?a.offsetWidth:u,d=s?a.offsetHeight:o,p=Co(u)!==f||Co(o)!==d;return p&&(u=f,o=d),{width:u,height:o,$:p}}function Ps(a){return Zt(a)?a:a.contextElement}function Sa(a){const r=Ps(a);if(!nn(r))return en(1);const u=r.getBoundingClientRect(),{width:o,height:s,$:f}=zv(r);let d=(f?Co(u.width):u.width)/o,p=(f?Co(u.height):u.height)/s;return(!d||!Number.isFinite(d))&&(d=1),(!p||!Number.isFinite(p))&&(p=1),{x:d,y:p}}const P1=en(0);function jv(a){const r=Dt(a);return!$s()||!r.visualViewport?P1:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function W1(a,r,u){return r===void 0&&(r=!1),!u||r&&u!==Dt(a)?!1:r}function Ol(a,r,u,o){r===void 0&&(r=!1),u===void 0&&(u=!1);const s=a.getBoundingClientRect(),f=Ps(a);let d=en(1);r&&(o?Zt(o)&&(d=Sa(o)):d=Sa(a));const p=W1(f,u,o)?jv(f):en(0);let v=(s.left+p.x)/d.x,m=(s.top+p.y)/d.y,b=s.width/d.x,w=s.height/d.y;if(f){const E=Dt(f),C=o&&Zt(o)?Dt(o):o;let A=E,S=Cs(A);for(;S&&o&&C!==A;){const N=Sa(S),O=S.getBoundingClientRect(),M=Kt(S),D=O.left+(S.clientLeft+parseFloat(M.paddingLeft))*N.x,z=O.top+(S.clientTop+parseFloat(M.paddingTop))*N.y;v*=N.x,m*=N.y,b*=N.x,w*=N.y,v+=D,m+=z,A=Dt(S),S=Cs(A)}}return Oo({width:b,height:w,x:v,y:m})}function Ws(a,r){const u=Lo(a).scrollLeft;return r?r.left+u:Ol(an(a)).left+u}function Uv(a,r,u){u===void 0&&(u=!1);const o=a.getBoundingClientRect(),s=o.left+r.scrollLeft-(u?0:Ws(a,o)),f=o.top+r.scrollTop;return{x:s,y:f}}function F1(a){let{elements:r,rect:u,offsetParent:o,strategy:s}=a;const f=s==="fixed",d=an(o),p=r?Uo(r.floating):!1;if(o===d||p&&f)return u;let v={scrollLeft:0,scrollTop:0},m=en(1);const b=en(0),w=nn(o);if((w||!w&&!f)&&((ja(o)!=="body"||Pi(d))&&(v=Lo(o)),nn(o))){const C=Ol(o);m=Sa(o),b.x=C.x+o.clientLeft,b.y=C.y+o.clientTop}const E=d&&!w&&!f?Uv(d,v,!0):en(0);return{width:u.width*m.x,height:u.height*m.y,x:u.x*m.x-v.scrollLeft*m.x+b.x+E.x,y:u.y*m.y-v.scrollTop*m.y+b.y+E.y}}function I1(a){return Array.from(a.getClientRects())}function ew(a){const r=an(a),u=Lo(a),o=a.ownerDocument.body,s=_t(r.scrollWidth,r.clientWidth,o.scrollWidth,o.clientWidth),f=_t(r.scrollHeight,r.clientHeight,o.scrollHeight,o.clientHeight);let d=-u.scrollLeft+Ws(a);const p=-u.scrollTop;return Kt(o).direction==="rtl"&&(d+=_t(r.clientWidth,o.clientWidth)-s),{width:s,height:f,x:d,y:p}}function tw(a,r){const u=Dt(a),o=an(a),s=u.visualViewport;let f=o.clientWidth,d=o.clientHeight,p=0,v=0;if(s){f=s.width,d=s.height;const m=$s();(!m||m&&r==="fixed")&&(p=s.offsetLeft,v=s.offsetTop)}return{width:f,height:d,x:p,y:v}}function nw(a,r){const u=Ol(a,!0,r==="fixed"),o=u.top+a.clientTop,s=u.left+a.clientLeft,f=nn(a)?Sa(a):en(1),d=a.clientWidth*f.x,p=a.clientHeight*f.y,v=s*f.x,m=o*f.y;return{width:d,height:p,x:v,y:m}}function _p(a,r,u){let o;if(r==="viewport")o=tw(a,u);else if(r==="document")o=ew(an(a));else if(Zt(r))o=nw(r,u);else{const s=jv(a);o={x:r.x-s.x,y:r.y-s.y,width:r.width,height:r.height}}return Oo(o)}function Lv(a,r){const u=nl(a);return u===r||!Zt(u)||Na(u)?!1:Kt(u).position==="fixed"||Lv(u,r)}function lw(a,r){const u=r.get(a);if(u)return u;let o=Qi(a,[],!1).filter(p=>Zt(p)&&ja(p)!=="body"),s=null;const f=Kt(a).position==="fixed";let d=f?nl(a):a;for(;Zt(d)&&!Na(d);){const p=Kt(d),v=Js(d);!v&&p.position==="fixed"&&(s=null),(f?!v&&!s:!v&&p.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||Pi(d)&&!v&&Lv(a,d))?o=o.filter(b=>b!==d):s=p,d=nl(d)}return r.set(a,o),o}function aw(a){let{element:r,boundary:u,rootBoundary:o,strategy:s}=a;const d=[...u==="clippingAncestors"?Uo(r)?[]:lw(r,this._c):[].concat(u),o],p=d[0],v=d.reduce((m,b)=>{const w=_p(r,b,s);return m.top=_t(w.top,m.top),m.right=tl(w.right,m.right),m.bottom=tl(w.bottom,m.bottom),m.left=_t(w.left,m.left),m},_p(r,p,s));return{width:v.right-v.left,height:v.bottom-v.top,x:v.left,y:v.top}}function iw(a){const{width:r,height:u}=zv(a);return{width:r,height:u}}function rw(a,r,u){const o=nn(r),s=an(r),f=u==="fixed",d=Ol(a,!0,f,r);let p={scrollLeft:0,scrollTop:0};const v=en(0);function m(){v.x=Ws(s)}if(o||!o&&!f)if((ja(r)!=="body"||Pi(s))&&(p=Lo(r)),o){const C=Ol(r,!0,f,r);v.x=C.x+r.clientLeft,v.y=C.y+r.clientTop}else s&&m();f&&!o&&s&&m();const b=s&&!o&&!f?Uv(s,p):en(0),w=d.left+p.scrollLeft-v.x-b.x,E=d.top+p.scrollTop-v.y-b.y;return{x:w,y:E,width:d.width,height:d.height}}function ds(a){return Kt(a).position==="static"}function Dp(a,r){if(!nn(a)||Kt(a).position==="fixed")return null;if(r)return r(a);let u=a.offsetParent;return an(a)===u&&(u=u.ownerDocument.body),u}function Hv(a,r){const u=Dt(a);if(Uo(a))return u;if(!nn(a)){let s=nl(a);for(;s&&!Na(s);){if(Zt(s)&&!ds(s))return s;s=nl(s)}return u}let o=Dp(a,r);for(;o&&J1(o)&&ds(o);)o=Dp(o,r);return o&&Na(o)&&ds(o)&&!Js(o)?u:o||$1(a)||u}const ow=async function(a){const r=this.getOffsetParent||Hv,u=this.getDimensions,o=await u(a.floating);return{reference:rw(a.reference,await r(a.floating),a.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function uw(a){return Kt(a).direction==="rtl"}const cw={convertOffsetParentRelativeRectToViewportRelativeRect:F1,getDocumentElement:an,getClippingRect:aw,getOffsetParent:Hv,getElementRects:ow,getClientRects:I1,getDimensions:iw,getScale:Sa,isElement:Zt,isRTL:uw};function Bv(a,r){return a.x===r.x&&a.y===r.y&&a.width===r.width&&a.height===r.height}function sw(a,r){let u=null,o;const s=an(a);function f(){var p;clearTimeout(o),(p=u)==null||p.disconnect(),u=null}function d(p,v){p===void 0&&(p=!1),v===void 0&&(v=1),f();const m=a.getBoundingClientRect(),{left:b,top:w,width:E,height:C}=m;if(p||r(),!E||!C)return;const A=po(w),S=po(s.clientWidth-(b+E)),N=po(s.clientHeight-(w+C)),O=po(b),D={rootMargin:-A+"px "+-S+"px "+-N+"px "+-O+"px",threshold:_t(0,tl(1,v))||1};let z=!0;function q(P){const J=P[0].intersectionRatio;if(J!==v){if(!z)return d();J?d(!1,J):o=setTimeout(()=>{d(!1,1e-7)},1e3)}J===1&&!Bv(m,a.getBoundingClientRect())&&d(),z=!1}try{u=new IntersectionObserver(q,{...D,root:s.ownerDocument})}catch{u=new IntersectionObserver(q,D)}u.observe(a)}return d(!0),f}function fw(a,r,u,o){o===void 0&&(o={});const{ancestorScroll:s=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:p=typeof IntersectionObserver=="function",animationFrame:v=!1}=o,m=Ps(a),b=s||f?[...m?Qi(m):[],...Qi(r)]:[];b.forEach(O=>{s&&O.addEventListener("scroll",u,{passive:!0}),f&&O.addEventListener("resize",u)});const w=m&&p?sw(m,u):null;let E=-1,C=null;d&&(C=new ResizeObserver(O=>{let[M]=O;M&&M.target===m&&C&&(C.unobserve(r),cancelAnimationFrame(E),E=requestAnimationFrame(()=>{var D;(D=C)==null||D.observe(r)})),u()}),m&&!v&&C.observe(m),C.observe(r));let A,S=v?Ol(a):null;v&&N();function N(){const O=Ol(a);S&&!Bv(S,O)&&u(),S=O,A=requestAnimationFrame(N)}return u(),()=>{var O;b.forEach(M=>{s&&M.removeEventListener("scroll",u),f&&M.removeEventListener("resize",u)}),w==null||w(),(O=C)==null||O.disconnect(),C=null,v&&cancelAnimationFrame(A)}}const dw=X1,mw=Q1,hw=Y1,pw=K1,vw=V1,zp=q1,gw=Z1,yw=(a,r,u)=>{const o=new Map,s={platform:cw,...u},f={...s.platform,_c:o};return k1(a,r,{...s,platform:f})};var wo=typeof document<"u"?y.useLayoutEffect:y.useEffect;function Mo(a,r){if(a===r)return!0;if(typeof a!=typeof r)return!1;if(typeof a=="function"&&a.toString()===r.toString())return!0;let u,o,s;if(a&&r&&typeof a=="object"){if(Array.isArray(a)){if(u=a.length,u!==r.length)return!1;for(o=u;o--!==0;)if(!Mo(a[o],r[o]))return!1;return!0}if(s=Object.keys(a),u=s.length,u!==Object.keys(r).length)return!1;for(o=u;o--!==0;)if(!{}.hasOwnProperty.call(r,s[o]))return!1;for(o=u;o--!==0;){const f=s[o];if(!(f==="_owner"&&a.$$typeof)&&!Mo(a[f],r[f]))return!1}return!0}return a!==a&&r!==r}function kv(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function jp(a,r){const u=kv(a);return Math.round(r*u)/u}function ms(a){const r=y.useRef(a);return wo(()=>{r.current=a}),r}function bw(a){a===void 0&&(a={});const{placement:r="bottom",strategy:u="absolute",middleware:o=[],platform:s,elements:{reference:f,floating:d}={},transform:p=!0,whileElementsMounted:v,open:m}=a,[b,w]=y.useState({x:0,y:0,strategy:u,placement:r,middlewareData:{},isPositioned:!1}),[E,C]=y.useState(o);Mo(E,o)||C(o);const[A,S]=y.useState(null),[N,O]=y.useState(null),M=y.useCallback(Y=>{Y!==P.current&&(P.current=Y,S(Y))},[]),D=y.useCallback(Y=>{Y!==J.current&&(J.current=Y,O(Y))},[]),z=f||A,q=d||N,P=y.useRef(null),J=y.useRef(null),K=y.useRef(b),ee=v!=null,fe=ms(v),pe=ms(s),me=ms(m),ge=y.useCallback(()=>{if(!P.current||!J.current)return;const Y={placement:r,strategy:u,middleware:E};pe.current&&(Y.platform=pe.current),yw(P.current,J.current,Y).then(ae=>{const R={...ae,isPositioned:me.current!==!1};xe.current&&!Mo(K.current,R)&&(K.current=R,$i.flushSync(()=>{w(R)}))})},[E,r,u,pe,me]);wo(()=>{m===!1&&K.current.isPositioned&&(K.current.isPositioned=!1,w(Y=>({...Y,isPositioned:!1})))},[m]);const xe=y.useRef(!1);wo(()=>(xe.current=!0,()=>{xe.current=!1}),[]),wo(()=>{if(z&&(P.current=z),q&&(J.current=q),z&&q){if(fe.current)return fe.current(z,q,ge);ge()}},[z,q,ge,fe,ee]);const ue=y.useMemo(()=>({reference:P,floating:J,setReference:M,setFloating:D}),[M,D]),L=y.useMemo(()=>({reference:z,floating:q}),[z,q]),Z=y.useMemo(()=>{const Y={position:u,left:0,top:0};if(!L.floating)return Y;const ae=jp(L.floating,b.x),R=jp(L.floating,b.y);return p?{...Y,transform:"translate("+ae+"px, "+R+"px)",...kv(L.floating)>=1.5&&{willChange:"transform"}}:{position:u,left:ae,top:R}},[u,p,L.floating,b.x,b.y]);return y.useMemo(()=>({...b,update:ge,refs:ue,elements:L,floatingStyles:Z}),[b,ge,ue,L,Z])}const xw=a=>{function r(u){return{}.hasOwnProperty.call(u,"current")}return{name:"arrow",options:a,fn(u){const{element:o,padding:s}=typeof a=="function"?a(u):a;return o&&r(o)?o.current!=null?zp({element:o.current,padding:s}).fn(u):{}:o?zp({element:o,padding:s}).fn(u):{}}}},Sw=(a,r)=>({...dw(a),options:[a,r]}),ww=(a,r)=>({...mw(a),options:[a,r]}),Ew=(a,r)=>({...gw(a),options:[a,r]}),Tw=(a,r)=>({...hw(a),options:[a,r]}),Aw=(a,r)=>({...pw(a),options:[a,r]}),Rw=(a,r)=>({...vw(a),options:[a,r]}),Cw=(a,r)=>({...xw(a),options:[a,r]});var Nw="Arrow",qv=y.forwardRef((a,r)=>{const{children:u,width:o=10,height:s=5,...f}=a;return x.jsx(Ge.svg,{...f,ref:r,width:o,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?u:x.jsx("polygon",{points:"0,0 30,0 15,10"})})});qv.displayName=Nw;var Ow=qv;function Mw(a){const[r,u]=y.useState(void 0);return St(()=>{if(a){u({width:a.offsetWidth,height:a.offsetHeight});const o=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const f=s[0];let d,p;if("borderBoxSize"in f){const v=f.borderBoxSize,m=Array.isArray(v)?v[0]:v;d=m.inlineSize,p=m.blockSize}else d=a.offsetWidth,p=a.offsetHeight;u({width:d,height:p})});return o.observe(a,{box:"border-box"}),()=>o.unobserve(a)}else u(void 0)},[a]),r}var Fs="Popper",[Yv,Vv]=Gs(Fs),[_w,Gv]=Yv(Fs),Xv=a=>{const{__scopePopper:r,children:u}=a,[o,s]=y.useState(null);return x.jsx(_w,{scope:r,anchor:o,onAnchorChange:s,children:u})};Xv.displayName=Fs;var Qv="PopperAnchor",Zv=y.forwardRef((a,r)=>{const{__scopePopper:u,virtualRef:o,...s}=a,f=Gv(Qv,u),d=y.useRef(null),p=lt(r,d);return y.useEffect(()=>{f.onAnchorChange((o==null?void 0:o.current)||d.current)}),o?null:x.jsx(Ge.div,{...s,ref:p})});Zv.displayName=Qv;var Is="PopperContent",[Dw,zw]=Yv(Is),Kv=y.forwardRef((a,r)=>{var F,oe,Me,Re,we,Ee;const{__scopePopper:u,side:o="bottom",sideOffset:s=0,align:f="center",alignOffset:d=0,arrowPadding:p=0,avoidCollisions:v=!0,collisionBoundary:m=[],collisionPadding:b=0,sticky:w="partial",hideWhenDetached:E=!1,updatePositionStrategy:C="optimized",onPlaced:A,...S}=a,N=Gv(Is,u),[O,M]=y.useState(null),D=lt(r,it=>M(it)),[z,q]=y.useState(null),P=Mw(z),J=(P==null?void 0:P.width)??0,K=(P==null?void 0:P.height)??0,ee=o+(f!=="center"?"-"+f:""),fe=typeof b=="number"?b:{top:0,right:0,bottom:0,left:0,...b},pe=Array.isArray(m)?m:[m],me=pe.length>0,ge={padding:fe,boundary:pe.filter(Uw),altBoundary:me},{refs:xe,floatingStyles:ue,placement:L,isPositioned:Z,middlewareData:Y}=bw({strategy:"fixed",placement:ee,whileElementsMounted:(...it)=>fw(...it,{animationFrame:C==="always"}),elements:{reference:N.anchor},middleware:[Sw({mainAxis:s+K,alignmentAxis:d}),v&&ww({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?Ew():void 0,...ge}),v&&Tw({...ge}),Aw({...ge,apply:({elements:it,rects:pt,availableWidth:rl,availableHeight:ol})=>{const{width:ct,height:Yo}=pt.reference,ul=it.floating.style;ul.setProperty("--radix-popper-available-width",`${rl}px`),ul.setProperty("--radix-popper-available-height",`${ol}px`),ul.setProperty("--radix-popper-anchor-width",`${ct}px`),ul.setProperty("--radix-popper-anchor-height",`${Yo}px`)}}),z&&Cw({element:z,padding:p}),Lw({arrowWidth:J,arrowHeight:K}),E&&Rw({strategy:"referenceHidden",...ge})]}),[ae,R]=Pv(L),G=Nl(A);St(()=>{Z&&(G==null||G())},[Z,G]);const W=(F=Y.arrow)==null?void 0:F.x,$=(oe=Y.arrow)==null?void 0:oe.y,I=((Me=Y.arrow)==null?void 0:Me.centerOffset)!==0,[he,re]=y.useState();return St(()=>{O&&re(window.getComputedStyle(O).zIndex)},[O]),x.jsx("div",{ref:xe.setFloating,"data-radix-popper-content-wrapper":"",style:{...ue,transform:Z?ue.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[(Re=Y.transformOrigin)==null?void 0:Re.x,(we=Y.transformOrigin)==null?void 0:we.y].join(" "),...((Ee=Y.hide)==null?void 0:Ee.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:x.jsx(Dw,{scope:u,placedSide:ae,onArrowChange:q,arrowX:W,arrowY:$,shouldHideArrow:I,children:x.jsx(Ge.div,{"data-side":ae,"data-align":R,...S,ref:D,style:{...S.style,animation:Z?void 0:"none"}})})})});Kv.displayName=Is;var Jv="PopperArrow",jw={top:"bottom",right:"left",bottom:"top",left:"right"},$v=y.forwardRef(function(r,u){const{__scopePopper:o,...s}=r,f=zw(Jv,o),d=jw[f.placedSide];return x.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:x.jsx(Ow,{...s,ref:u,style:{...s.style,display:"block"}})})});$v.displayName=Jv;function Uw(a){return a!==null}var Lw=a=>({name:"transformOrigin",options:a,fn(r){var N,O,M;const{placement:u,rects:o,middlewareData:s}=r,d=((N=s.arrow)==null?void 0:N.centerOffset)!==0,p=d?0:a.arrowWidth,v=d?0:a.arrowHeight,[m,b]=Pv(u),w={start:"0%",center:"50%",end:"100%"}[b],E=(((O=s.arrow)==null?void 0:O.x)??0)+p/2,C=(((M=s.arrow)==null?void 0:M.y)??0)+v/2;let A="",S="";return m==="bottom"?(A=d?w:`${E}px`,S=`${-v}px`):m==="top"?(A=d?w:`${E}px`,S=`${o.floating.height+v}px`):m==="right"?(A=`${-v}px`,S=d?w:`${C}px`):m==="left"&&(A=`${o.floating.width+v}px`,S=d?w:`${C}px`),{data:{x:A,y:S}}}});function Pv(a){const[r,u="center"]=a.split("-");return[r,u]}var Hw=Xv,Bw=Zv,kw=Kv,qw=$v,Yw="Portal",Wv=y.forwardRef((a,r)=>{var p;const{container:u,...o}=a,[s,f]=y.useState(!1);St(()=>f(!0),[]);const d=u||s&&((p=globalThis==null?void 0:globalThis.document)==null?void 0:p.body);return d?jx.createPortal(x.jsx(Ge.div,{...o,ref:r}),d):null});Wv.displayName=Yw;var Vw=Gp[" useInsertionEffect ".trim().toString()]||St;function Up({prop:a,defaultProp:r,onChange:u=()=>{},caller:o}){const[s,f,d]=Gw({defaultProp:r,onChange:u}),p=a!==void 0,v=p?a:s;{const b=y.useRef(a!==void 0);y.useEffect(()=>{const w=b.current;w!==p&&console.warn(`${o} is changing from ${w?"controlled":"uncontrolled"} to ${p?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),b.current=p},[p,o])}const m=y.useCallback(b=>{var w;if(p){const E=Xw(b)?b(a):b;E!==a&&((w=d.current)==null||w.call(d,E))}else f(b)},[p,a,f,d]);return[v,m]}function Gw({defaultProp:a,onChange:r}){const[u,o]=y.useState(a),s=y.useRef(u),f=y.useRef(r);return Vw(()=>{f.current=r},[r]),y.useEffect(()=>{var d;s.current!==u&&((d=f.current)==null||d.call(f,u),s.current=u)},[u,s]),[u,o,f]}function Xw(a){return typeof a=="function"}function Qw(a){const r=y.useRef({value:a,previous:a});return y.useMemo(()=>(r.current.value!==a&&(r.current.previous=r.current.value,r.current.value=a),r.current.previous),[a])}var Fv=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Zw="VisuallyHidden",Kw=y.forwardRef((a,r)=>x.jsx(Ge.span,{...a,ref:r,style:{...Fv,...a.style}}));Kw.displayName=Zw;var Jw=function(a){if(typeof document>"u")return null;var r=Array.isArray(a)?a[0]:a;return r.ownerDocument.body},ya=new WeakMap,vo=new WeakMap,go={},hs=0,Iv=function(a){return a&&(a.host||Iv(a.parentNode))},$w=function(a,r){return r.map(function(u){if(a.contains(u))return u;var o=Iv(u);return o&&a.contains(o)?o:(console.error("aria-hidden",u,"in not contained inside",a,". Doing nothing"),null)}).filter(function(u){return!!u})},Pw=function(a,r,u,o){var s=$w(r,Array.isArray(a)?a:[a]);go[u]||(go[u]=new WeakMap);var f=go[u],d=[],p=new Set,v=new Set(s),m=function(w){!w||p.has(w)||(p.add(w),m(w.parentNode))};s.forEach(m);var b=function(w){!w||v.has(w)||Array.prototype.forEach.call(w.children,function(E){if(p.has(E))b(E);else try{var C=E.getAttribute(o),A=C!==null&&C!=="false",S=(ya.get(E)||0)+1,N=(f.get(E)||0)+1;ya.set(E,S),f.set(E,N),d.push(E),S===1&&A&&vo.set(E,!0),N===1&&E.setAttribute(u,"true"),A||E.setAttribute(o,"true")}catch(O){console.error("aria-hidden: cannot operate on ",E,O)}})};return b(r),p.clear(),hs++,function(){d.forEach(function(w){var E=ya.get(w)-1,C=f.get(w)-1;ya.set(w,E),f.set(w,C),E||(vo.has(w)||w.removeAttribute(o),vo.delete(w)),C||w.removeAttribute(u)}),hs--,hs||(ya=new WeakMap,ya=new WeakMap,vo=new WeakMap,go={})}},Ww=function(a,r,u){u===void 0&&(u="data-aria-hidden");var o=Array.from(Array.isArray(a)?a:[a]),s=Jw(a);return s?(o.push.apply(o,Array.from(s.querySelectorAll("[aria-live]"))),Pw(o,s,u,"aria-hidden")):function(){return null}},It=function(){return It=Object.assign||function(r){for(var u,o=1,s=arguments.length;o<s;o++){u=arguments[o];for(var f in u)Object.prototype.hasOwnProperty.call(u,f)&&(r[f]=u[f])}return r},It.apply(this,arguments)};function eg(a,r){var u={};for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&r.indexOf(o)<0&&(u[o]=a[o]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,o=Object.getOwnPropertySymbols(a);s<o.length;s++)r.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(a,o[s])&&(u[o[s]]=a[o[s]]);return u}function Fw(a,r,u){if(u||arguments.length===2)for(var o=0,s=r.length,f;o<s;o++)(f||!(o in r))&&(f||(f=Array.prototype.slice.call(r,0,o)),f[o]=r[o]);return a.concat(f||Array.prototype.slice.call(r))}var Eo="right-scroll-bar-position",To="width-before-scroll-bar",Iw="with-scroll-bars-hidden",eE="--removed-body-scroll-bar-size";function ps(a,r){return typeof a=="function"?a(r):a&&(a.current=r),a}function tE(a,r){var u=y.useState(function(){return{value:a,callback:r,facade:{get current(){return u.value},set current(o){var s=u.value;s!==o&&(u.value=o,u.callback(o,s))}}}})[0];return u.callback=r,u.facade}var nE=typeof window<"u"?y.useLayoutEffect:y.useEffect,Lp=new WeakMap;function lE(a,r){var u=tE(null,function(o){return a.forEach(function(s){return ps(s,o)})});return nE(function(){var o=Lp.get(u);if(o){var s=new Set(o),f=new Set(a),d=u.current;s.forEach(function(p){f.has(p)||ps(p,null)}),f.forEach(function(p){s.has(p)||ps(p,d)})}Lp.set(u,a)},[a]),u}function aE(a){return a}function iE(a,r){r===void 0&&(r=aE);var u=[],o=!1,s={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return u.length?u[u.length-1]:a},useMedium:function(f){var d=r(f,o);return u.push(d),function(){u=u.filter(function(p){return p!==d})}},assignSyncMedium:function(f){for(o=!0;u.length;){var d=u;u=[],d.forEach(f)}u={push:function(p){return f(p)},filter:function(){return u}}},assignMedium:function(f){o=!0;var d=[];if(u.length){var p=u;u=[],p.forEach(f),d=u}var v=function(){var b=d;d=[],b.forEach(f)},m=function(){return Promise.resolve().then(v)};m(),u={push:function(b){d.push(b),m()},filter:function(b){return d=d.filter(b),u}}}};return s}function rE(a){a===void 0&&(a={});var r=iE(null);return r.options=It({async:!0,ssr:!1},a),r}var tg=function(a){var r=a.sideCar,u=eg(a,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=r.read();if(!o)throw new Error("Sidecar medium not found");return y.createElement(o,It({},u))};tg.isSideCarExport=!0;function oE(a,r){return a.useMedium(r),tg}var ng=rE(),vs=function(){},Ho=y.forwardRef(function(a,r){var u=y.useRef(null),o=y.useState({onScrollCapture:vs,onWheelCapture:vs,onTouchMoveCapture:vs}),s=o[0],f=o[1],d=a.forwardProps,p=a.children,v=a.className,m=a.removeScrollBar,b=a.enabled,w=a.shards,E=a.sideCar,C=a.noIsolation,A=a.inert,S=a.allowPinchZoom,N=a.as,O=N===void 0?"div":N,M=a.gapMode,D=eg(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=E,q=lE([u,r]),P=It(It({},D),s);return y.createElement(y.Fragment,null,b&&y.createElement(z,{sideCar:ng,removeScrollBar:m,shards:w,noIsolation:C,inert:A,setCallbacks:f,allowPinchZoom:!!S,lockRef:u,gapMode:M}),d?y.cloneElement(y.Children.only(p),It(It({},P),{ref:q})):y.createElement(O,It({},P,{className:v,ref:q}),p))});Ho.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ho.classNames={fullWidth:To,zeroRight:Eo};var uE=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function cE(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var r=uE();return r&&a.setAttribute("nonce",r),a}function sE(a,r){a.styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r))}function fE(a){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(a)}var dE=function(){var a=0,r=null;return{add:function(u){a==0&&(r=cE())&&(sE(r,u),fE(r)),a++},remove:function(){a--,!a&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},mE=function(){var a=dE();return function(r,u){y.useEffect(function(){return a.add(r),function(){a.remove()}},[r&&u])}},lg=function(){var a=mE(),r=function(u){var o=u.styles,s=u.dynamic;return a(o,s),null};return r},hE={left:0,top:0,right:0,gap:0},gs=function(a){return parseInt(a||"",10)||0},pE=function(a){var r=window.getComputedStyle(document.body),u=r[a==="padding"?"paddingLeft":"marginLeft"],o=r[a==="padding"?"paddingTop":"marginTop"],s=r[a==="padding"?"paddingRight":"marginRight"];return[gs(u),gs(o),gs(s)]},vE=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return hE;var r=pE(a),u=document.documentElement.clientWidth,o=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,o-u+r[2]-r[0])}},gE=lg(),wa="data-scroll-locked",yE=function(a,r,u,o){var s=a.left,f=a.top,d=a.right,p=a.gap;return u===void 0&&(u="margin"),`
  .`.concat(Iw,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(p,"px ").concat(o,`;
  }
  body[`).concat(wa,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(o,";"),u==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(p,"px ").concat(o,`;
    `),u==="padding"&&"padding-right: ".concat(p,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Eo,` {
    right: `).concat(p,"px ").concat(o,`;
  }
  
  .`).concat(To,` {
    margin-right: `).concat(p,"px ").concat(o,`;
  }
  
  .`).concat(Eo," .").concat(Eo,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(To," .").concat(To,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(wa,`] {
    `).concat(eE,": ").concat(p,`px;
  }
`)},Hp=function(){var a=parseInt(document.body.getAttribute(wa)||"0",10);return isFinite(a)?a:0},bE=function(){y.useEffect(function(){return document.body.setAttribute(wa,(Hp()+1).toString()),function(){var a=Hp()-1;a<=0?document.body.removeAttribute(wa):document.body.setAttribute(wa,a.toString())}},[])},xE=function(a){var r=a.noRelative,u=a.noImportant,o=a.gapMode,s=o===void 0?"margin":o;bE();var f=y.useMemo(function(){return vE(s)},[s]);return y.createElement(gE,{styles:yE(f,!r,s,u?"":"!important")})},Ns=!1;if(typeof window<"u")try{var yo=Object.defineProperty({},"passive",{get:function(){return Ns=!0,!0}});window.addEventListener("test",yo,yo),window.removeEventListener("test",yo,yo)}catch{Ns=!1}var ba=Ns?{passive:!1}:!1,SE=function(a){return a.tagName==="TEXTAREA"},ag=function(a,r){if(!(a instanceof Element))return!1;var u=window.getComputedStyle(a);return u[r]!=="hidden"&&!(u.overflowY===u.overflowX&&!SE(a)&&u[r]==="visible")},wE=function(a){return ag(a,"overflowY")},EE=function(a){return ag(a,"overflowX")},Bp=function(a,r){var u=r.ownerDocument,o=r;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var s=ig(a,o);if(s){var f=rg(a,o),d=f[1],p=f[2];if(d>p)return!0}o=o.parentNode}while(o&&o!==u.body);return!1},TE=function(a){var r=a.scrollTop,u=a.scrollHeight,o=a.clientHeight;return[r,u,o]},AE=function(a){var r=a.scrollLeft,u=a.scrollWidth,o=a.clientWidth;return[r,u,o]},ig=function(a,r){return a==="v"?wE(r):EE(r)},rg=function(a,r){return a==="v"?TE(r):AE(r)},RE=function(a,r){return a==="h"&&r==="rtl"?-1:1},CE=function(a,r,u,o,s){var f=RE(a,window.getComputedStyle(r).direction),d=f*o,p=u.target,v=r.contains(p),m=!1,b=d>0,w=0,E=0;do{var C=rg(a,p),A=C[0],S=C[1],N=C[2],O=S-N-f*A;(A||O)&&ig(a,p)&&(w+=O,E+=A),p instanceof ShadowRoot?p=p.host:p=p.parentNode}while(!v&&p!==document.body||v&&(r.contains(p)||r===p));return(b&&Math.abs(w)<1||!b&&Math.abs(E)<1)&&(m=!0),m},bo=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},kp=function(a){return[a.deltaX,a.deltaY]},qp=function(a){return a&&"current"in a?a.current:a},NE=function(a,r){return a[0]===r[0]&&a[1]===r[1]},OE=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},ME=0,xa=[];function _E(a){var r=y.useRef([]),u=y.useRef([0,0]),o=y.useRef(),s=y.useState(ME++)[0],f=y.useState(lg)[0],d=y.useRef(a);y.useEffect(function(){d.current=a},[a]),y.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(s));var S=Fw([a.lockRef.current],(a.shards||[]).map(qp),!0).filter(Boolean);return S.forEach(function(N){return N.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),S.forEach(function(N){return N.classList.remove("allow-interactivity-".concat(s))})}}},[a.inert,a.lockRef.current,a.shards]);var p=y.useCallback(function(S,N){if("touches"in S&&S.touches.length===2||S.type==="wheel"&&S.ctrlKey)return!d.current.allowPinchZoom;var O=bo(S),M=u.current,D="deltaX"in S?S.deltaX:M[0]-O[0],z="deltaY"in S?S.deltaY:M[1]-O[1],q,P=S.target,J=Math.abs(D)>Math.abs(z)?"h":"v";if("touches"in S&&J==="h"&&P.type==="range")return!1;var K=Bp(J,P);if(!K)return!0;if(K?q=J:(q=J==="v"?"h":"v",K=Bp(J,P)),!K)return!1;if(!o.current&&"changedTouches"in S&&(D||z)&&(o.current=q),!q)return!0;var ee=o.current||q;return CE(ee,N,S,ee==="h"?D:z)},[]),v=y.useCallback(function(S){var N=S;if(!(!xa.length||xa[xa.length-1]!==f)){var O="deltaY"in N?kp(N):bo(N),M=r.current.filter(function(q){return q.name===N.type&&(q.target===N.target||N.target===q.shadowParent)&&NE(q.delta,O)})[0];if(M&&M.should){N.cancelable&&N.preventDefault();return}if(!M){var D=(d.current.shards||[]).map(qp).filter(Boolean).filter(function(q){return q.contains(N.target)}),z=D.length>0?p(N,D[0]):!d.current.noIsolation;z&&N.cancelable&&N.preventDefault()}}},[]),m=y.useCallback(function(S,N,O,M){var D={name:S,delta:N,target:O,should:M,shadowParent:DE(O)};r.current.push(D),setTimeout(function(){r.current=r.current.filter(function(z){return z!==D})},1)},[]),b=y.useCallback(function(S){u.current=bo(S),o.current=void 0},[]),w=y.useCallback(function(S){m(S.type,kp(S),S.target,p(S,a.lockRef.current))},[]),E=y.useCallback(function(S){m(S.type,bo(S),S.target,p(S,a.lockRef.current))},[]);y.useEffect(function(){return xa.push(f),a.setCallbacks({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:E}),document.addEventListener("wheel",v,ba),document.addEventListener("touchmove",v,ba),document.addEventListener("touchstart",b,ba),function(){xa=xa.filter(function(S){return S!==f}),document.removeEventListener("wheel",v,ba),document.removeEventListener("touchmove",v,ba),document.removeEventListener("touchstart",b,ba)}},[]);var C=a.removeScrollBar,A=a.inert;return y.createElement(y.Fragment,null,A?y.createElement(f,{styles:OE(s)}):null,C?y.createElement(xE,{gapMode:a.gapMode}):null)}function DE(a){for(var r=null;a!==null;)a instanceof ShadowRoot&&(r=a.host,a=a.host),a=a.parentNode;return r}const zE=oE(ng,_E);var og=y.forwardRef(function(a,r){return y.createElement(Ho,It({},a,{ref:r,sideCar:zE}))});og.classNames=Ho.classNames;var jE=[" ","Enter","ArrowUp","ArrowDown"],UE=[" ","Enter"],Ml="Select",[Bo,ko,LE]=s1(Ml),[Ua,R2]=Gs(Ml,[LE,Vv]),qo=Vv(),[HE,al]=Ua(Ml),[BE,kE]=Ua(Ml),ug=a=>{const{__scopeSelect:r,children:u,open:o,defaultOpen:s,onOpenChange:f,value:d,defaultValue:p,onValueChange:v,dir:m,name:b,autoComplete:w,disabled:E,required:C,form:A}=a,S=qo(r),[N,O]=y.useState(null),[M,D]=y.useState(null),[z,q]=y.useState(!1),P=d1(m),[J,K]=Up({prop:o,defaultProp:s??!1,onChange:f,caller:Ml}),[ee,fe]=Up({prop:d,defaultProp:p,onChange:v,caller:Ml}),pe=y.useRef(null),me=N?A||!!N.closest("form"):!0,[ge,xe]=y.useState(new Set),ue=Array.from(ge).map(L=>L.props.value).join(";");return x.jsx(Hw,{...S,children:x.jsxs(HE,{required:C,scope:r,trigger:N,onTriggerChange:O,valueNode:M,onValueNodeChange:D,valueNodeHasChildren:z,onValueNodeHasChildrenChange:q,contentId:Xs(),value:ee,onValueChange:fe,open:J,onOpenChange:K,dir:P,triggerPointerDownPosRef:pe,disabled:E,children:[x.jsx(Bo.Provider,{scope:r,children:x.jsx(BE,{scope:a.__scopeSelect,onNativeOptionAdd:y.useCallback(L=>{xe(Z=>new Set(Z).add(L))},[]),onNativeOptionRemove:y.useCallback(L=>{xe(Z=>{const Y=new Set(Z);return Y.delete(L),Y})},[]),children:u})}),me?x.jsxs(Mg,{"aria-hidden":!0,required:C,tabIndex:-1,name:b,autoComplete:w,value:ee,onChange:L=>fe(L.target.value),disabled:E,form:A,children:[ee===void 0?x.jsx("option",{value:""}):null,Array.from(ge)]},ue):null]})})};ug.displayName=Ml;var cg="SelectTrigger",sg=y.forwardRef((a,r)=>{const{__scopeSelect:u,disabled:o=!1,...s}=a,f=qo(u),d=al(cg,u),p=d.disabled||o,v=lt(r,d.onTriggerChange),m=ko(u),b=y.useRef("touch"),[w,E,C]=Dg(S=>{const N=m().filter(D=>!D.disabled),O=N.find(D=>D.value===d.value),M=zg(N,S,O);M!==void 0&&d.onValueChange(M.value)}),A=S=>{p||(d.onOpenChange(!0),C()),S&&(d.triggerPointerDownPosRef.current={x:Math.round(S.pageX),y:Math.round(S.pageY)})};return x.jsx(Bw,{asChild:!0,...f,children:x.jsx(Ge.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:p,"data-disabled":p?"":void 0,"data-placeholder":_g(d.value)?"":void 0,...s,ref:v,onClick:Je(s.onClick,S=>{S.currentTarget.focus(),b.current!=="mouse"&&A(S)}),onPointerDown:Je(s.onPointerDown,S=>{b.current=S.pointerType;const N=S.target;N.hasPointerCapture(S.pointerId)&&N.releasePointerCapture(S.pointerId),S.button===0&&S.ctrlKey===!1&&S.pointerType==="mouse"&&(A(S),S.preventDefault())}),onKeyDown:Je(s.onKeyDown,S=>{const N=w.current!=="";!(S.ctrlKey||S.altKey||S.metaKey)&&S.key.length===1&&E(S.key),!(N&&S.key===" ")&&jE.includes(S.key)&&(A(),S.preventDefault())})})})});sg.displayName=cg;var fg="SelectValue",dg=y.forwardRef((a,r)=>{const{__scopeSelect:u,className:o,style:s,children:f,placeholder:d="",...p}=a,v=al(fg,u),{onValueNodeHasChildrenChange:m}=v,b=f!==void 0,w=lt(r,v.onValueNodeChange);return St(()=>{m(b)},[m,b]),x.jsx(Ge.span,{...p,ref:w,style:{pointerEvents:"none"},children:_g(v.value)?x.jsx(x.Fragment,{children:d}):f})});dg.displayName=fg;var qE="SelectIcon",mg=y.forwardRef((a,r)=>{const{__scopeSelect:u,children:o,...s}=a;return x.jsx(Ge.span,{"aria-hidden":!0,...s,ref:r,children:o||"▼"})});mg.displayName=qE;var YE="SelectPortal",hg=a=>x.jsx(Wv,{asChild:!0,...a});hg.displayName=YE;var _l="SelectContent",pg=y.forwardRef((a,r)=>{const u=al(_l,a.__scopeSelect),[o,s]=y.useState();if(St(()=>{s(new DocumentFragment)},[]),!u.open){const f=o;return f?$i.createPortal(x.jsx(vg,{scope:a.__scopeSelect,children:x.jsx(Bo.Slot,{scope:a.__scopeSelect,children:x.jsx("div",{children:a.children})})}),f):null}return x.jsx(gg,{...a,ref:r})});pg.displayName=_l;var Qt=10,[vg,il]=Ua(_l),VE="SelectContentImpl",GE=Vi("SelectContent.RemoveScroll"),gg=y.forwardRef((a,r)=>{const{__scopeSelect:u,position:o="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:f,onPointerDownOutside:d,side:p,sideOffset:v,align:m,alignOffset:b,arrowPadding:w,collisionBoundary:E,collisionPadding:C,sticky:A,hideWhenDetached:S,avoidCollisions:N,...O}=a,M=al(_l,u),[D,z]=y.useState(null),[q,P]=y.useState(null),J=lt(r,F=>z(F)),[K,ee]=y.useState(null),[fe,pe]=y.useState(null),me=ko(u),[ge,xe]=y.useState(!1),ue=y.useRef(!1);y.useEffect(()=>{if(D)return Ww(D)},[D]),S1();const L=y.useCallback(F=>{const[oe,...Me]=me().map(Ee=>Ee.ref.current),[Re]=Me.slice(-1),we=document.activeElement;for(const Ee of F)if(Ee===we||(Ee==null||Ee.scrollIntoView({block:"nearest"}),Ee===oe&&q&&(q.scrollTop=0),Ee===Re&&q&&(q.scrollTop=q.scrollHeight),Ee==null||Ee.focus(),document.activeElement!==we))return},[me,q]),Z=y.useCallback(()=>L([K,D]),[L,K,D]);y.useEffect(()=>{ge&&Z()},[ge,Z]);const{onOpenChange:Y,triggerPointerDownPosRef:ae}=M;y.useEffect(()=>{if(D){let F={x:0,y:0};const oe=Re=>{var we,Ee;F={x:Math.abs(Math.round(Re.pageX)-(((we=ae.current)==null?void 0:we.x)??0)),y:Math.abs(Math.round(Re.pageY)-(((Ee=ae.current)==null?void 0:Ee.y)??0))}},Me=Re=>{F.x<=10&&F.y<=10?Re.preventDefault():D.contains(Re.target)||Y(!1),document.removeEventListener("pointermove",oe),ae.current=null};return ae.current!==null&&(document.addEventListener("pointermove",oe),document.addEventListener("pointerup",Me,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",oe),document.removeEventListener("pointerup",Me,{capture:!0})}}},[D,Y,ae]),y.useEffect(()=>{const F=()=>Y(!1);return window.addEventListener("blur",F),window.addEventListener("resize",F),()=>{window.removeEventListener("blur",F),window.removeEventListener("resize",F)}},[Y]);const[R,G]=Dg(F=>{const oe=me().filter(we=>!we.disabled),Me=oe.find(we=>we.ref.current===document.activeElement),Re=zg(oe,F,Me);Re&&setTimeout(()=>Re.ref.current.focus())}),W=y.useCallback((F,oe,Me)=>{const Re=!ue.current&&!Me;(M.value!==void 0&&M.value===oe||Re)&&(ee(F),Re&&(ue.current=!0))},[M.value]),$=y.useCallback(()=>D==null?void 0:D.focus(),[D]),I=y.useCallback((F,oe,Me)=>{const Re=!ue.current&&!Me;(M.value!==void 0&&M.value===oe||Re)&&pe(F)},[M.value]),he=o==="popper"?Os:yg,re=he===Os?{side:p,sideOffset:v,align:m,alignOffset:b,arrowPadding:w,collisionBoundary:E,collisionPadding:C,sticky:A,hideWhenDetached:S,avoidCollisions:N}:{};return x.jsx(vg,{scope:u,content:D,viewport:q,onViewportChange:P,itemRefCallback:W,selectedItem:K,onItemLeave:$,itemTextRefCallback:I,focusSelectedItem:Z,selectedItemText:fe,position:o,isPositioned:ge,searchRef:R,children:x.jsx(og,{as:GE,allowPinchZoom:!0,children:x.jsx(Nv,{asChild:!0,trapped:M.open,onMountAutoFocus:F=>{F.preventDefault()},onUnmountAutoFocus:Je(s,F=>{var oe;(oe=M.trigger)==null||oe.focus({preventScroll:!0}),F.preventDefault()}),children:x.jsx(Rv,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:F=>F.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:x.jsx(he,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:F=>F.preventDefault(),...O,...re,onPlaced:()=>xe(!0),ref:J,style:{display:"flex",flexDirection:"column",outline:"none",...O.style},onKeyDown:Je(O.onKeyDown,F=>{const oe=F.ctrlKey||F.altKey||F.metaKey;if(F.key==="Tab"&&F.preventDefault(),!oe&&F.key.length===1&&G(F.key),["ArrowUp","ArrowDown","Home","End"].includes(F.key)){let Re=me().filter(we=>!we.disabled).map(we=>we.ref.current);if(["ArrowUp","End"].includes(F.key)&&(Re=Re.slice().reverse()),["ArrowUp","ArrowDown"].includes(F.key)){const we=F.target,Ee=Re.indexOf(we);Re=Re.slice(Ee+1)}setTimeout(()=>L(Re)),F.preventDefault()}})})})})})})});gg.displayName=VE;var XE="SelectItemAlignedPosition",yg=y.forwardRef((a,r)=>{const{__scopeSelect:u,onPlaced:o,...s}=a,f=al(_l,u),d=il(_l,u),[p,v]=y.useState(null),[m,b]=y.useState(null),w=lt(r,J=>b(J)),E=ko(u),C=y.useRef(!1),A=y.useRef(!0),{viewport:S,selectedItem:N,selectedItemText:O,focusSelectedItem:M}=d,D=y.useCallback(()=>{if(f.trigger&&f.valueNode&&p&&m&&S&&N&&O){const J=f.trigger.getBoundingClientRect(),K=m.getBoundingClientRect(),ee=f.valueNode.getBoundingClientRect(),fe=O.getBoundingClientRect();if(f.dir!=="rtl"){const we=fe.left-K.left,Ee=ee.left-we,it=J.left-Ee,pt=J.width+it,rl=Math.max(pt,K.width),ol=window.innerWidth-Qt,ct=bp(Ee,[Qt,Math.max(Qt,ol-rl)]);p.style.minWidth=pt+"px",p.style.left=ct+"px"}else{const we=K.right-fe.right,Ee=window.innerWidth-ee.right-we,it=window.innerWidth-J.right-Ee,pt=J.width+it,rl=Math.max(pt,K.width),ol=window.innerWidth-Qt,ct=bp(Ee,[Qt,Math.max(Qt,ol-rl)]);p.style.minWidth=pt+"px",p.style.right=ct+"px"}const pe=E(),me=window.innerHeight-Qt*2,ge=S.scrollHeight,xe=window.getComputedStyle(m),ue=parseInt(xe.borderTopWidth,10),L=parseInt(xe.paddingTop,10),Z=parseInt(xe.borderBottomWidth,10),Y=parseInt(xe.paddingBottom,10),ae=ue+L+ge+Y+Z,R=Math.min(N.offsetHeight*5,ae),G=window.getComputedStyle(S),W=parseInt(G.paddingTop,10),$=parseInt(G.paddingBottom,10),I=J.top+J.height/2-Qt,he=me-I,re=N.offsetHeight/2,F=N.offsetTop+re,oe=ue+L+F,Me=ae-oe;if(oe<=I){const we=pe.length>0&&N===pe[pe.length-1].ref.current;p.style.bottom="0px";const Ee=m.clientHeight-S.offsetTop-S.offsetHeight,it=Math.max(he,re+(we?$:0)+Ee+Z),pt=oe+it;p.style.height=pt+"px"}else{const we=pe.length>0&&N===pe[0].ref.current;p.style.top="0px";const it=Math.max(I,ue+S.offsetTop+(we?W:0)+re)+Me;p.style.height=it+"px",S.scrollTop=oe-I+S.offsetTop}p.style.margin=`${Qt}px 0`,p.style.minHeight=R+"px",p.style.maxHeight=me+"px",o==null||o(),requestAnimationFrame(()=>C.current=!0)}},[E,f.trigger,f.valueNode,p,m,S,N,O,f.dir,o]);St(()=>D(),[D]);const[z,q]=y.useState();St(()=>{m&&q(window.getComputedStyle(m).zIndex)},[m]);const P=y.useCallback(J=>{J&&A.current===!0&&(D(),M==null||M(),A.current=!1)},[D,M]);return x.jsx(ZE,{scope:u,contentWrapper:p,shouldExpandOnScrollRef:C,onScrollButtonChange:P,children:x.jsx("div",{ref:v,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:x.jsx(Ge.div,{...s,ref:w,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});yg.displayName=XE;var QE="SelectPopperPosition",Os=y.forwardRef((a,r)=>{const{__scopeSelect:u,align:o="start",collisionPadding:s=Qt,...f}=a,d=qo(u);return x.jsx(kw,{...d,...f,ref:r,align:o,collisionPadding:s,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Os.displayName=QE;var[ZE,ef]=Ua(_l,{}),Ms="SelectViewport",bg=y.forwardRef((a,r)=>{const{__scopeSelect:u,nonce:o,...s}=a,f=il(Ms,u),d=ef(Ms,u),p=lt(r,f.onViewportChange),v=y.useRef(0);return x.jsxs(x.Fragment,{children:[x.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),x.jsx(Bo.Slot,{scope:u,children:x.jsx(Ge.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:p,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:Je(s.onScroll,m=>{const b=m.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:E}=d;if(E!=null&&E.current&&w){const C=Math.abs(v.current-b.scrollTop);if(C>0){const A=window.innerHeight-Qt*2,S=parseFloat(w.style.minHeight),N=parseFloat(w.style.height),O=Math.max(S,N);if(O<A){const M=O+C,D=Math.min(A,M),z=M-D;w.style.height=D+"px",w.style.bottom==="0px"&&(b.scrollTop=z>0?z:0,w.style.justifyContent="flex-end")}}}v.current=b.scrollTop})})})]})});bg.displayName=Ms;var xg="SelectGroup",[KE,JE]=Ua(xg),$E=y.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=Xs();return x.jsx(KE,{scope:u,id:s,children:x.jsx(Ge.div,{role:"group","aria-labelledby":s,...o,ref:r})})});$E.displayName=xg;var Sg="SelectLabel",PE=y.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=JE(Sg,u);return x.jsx(Ge.div,{id:s.id,...o,ref:r})});PE.displayName=Sg;var _o="SelectItem",[WE,wg]=Ua(_o),Eg=y.forwardRef((a,r)=>{const{__scopeSelect:u,value:o,disabled:s=!1,textValue:f,...d}=a,p=al(_o,u),v=il(_o,u),m=p.value===o,[b,w]=y.useState(f??""),[E,C]=y.useState(!1),A=lt(r,M=>{var D;return(D=v.itemRefCallback)==null?void 0:D.call(v,M,o,s)}),S=Xs(),N=y.useRef("touch"),O=()=>{s||(p.onValueChange(o),p.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return x.jsx(WE,{scope:u,value:o,disabled:s,textId:S,isSelected:m,onItemTextChange:y.useCallback(M=>{w(D=>D||((M==null?void 0:M.textContent)??"").trim())},[]),children:x.jsx(Bo.ItemSlot,{scope:u,value:o,disabled:s,textValue:b,children:x.jsx(Ge.div,{role:"option","aria-labelledby":S,"data-highlighted":E?"":void 0,"aria-selected":m&&E,"data-state":m?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...d,ref:A,onFocus:Je(d.onFocus,()=>C(!0)),onBlur:Je(d.onBlur,()=>C(!1)),onClick:Je(d.onClick,()=>{N.current!=="mouse"&&O()}),onPointerUp:Je(d.onPointerUp,()=>{N.current==="mouse"&&O()}),onPointerDown:Je(d.onPointerDown,M=>{N.current=M.pointerType}),onPointerMove:Je(d.onPointerMove,M=>{var D;N.current=M.pointerType,s?(D=v.onItemLeave)==null||D.call(v):N.current==="mouse"&&M.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Je(d.onPointerLeave,M=>{var D;M.currentTarget===document.activeElement&&((D=v.onItemLeave)==null||D.call(v))}),onKeyDown:Je(d.onKeyDown,M=>{var z;((z=v.searchRef)==null?void 0:z.current)!==""&&M.key===" "||(UE.includes(M.key)&&O(),M.key===" "&&M.preventDefault())})})})})});Eg.displayName=_o;var Bi="SelectItemText",Tg=y.forwardRef((a,r)=>{const{__scopeSelect:u,className:o,style:s,...f}=a,d=al(Bi,u),p=il(Bi,u),v=wg(Bi,u),m=kE(Bi,u),[b,w]=y.useState(null),E=lt(r,O=>w(O),v.onItemTextChange,O=>{var M;return(M=p.itemTextRefCallback)==null?void 0:M.call(p,O,v.value,v.disabled)}),C=b==null?void 0:b.textContent,A=y.useMemo(()=>x.jsx("option",{value:v.value,disabled:v.disabled,children:C},v.value),[v.disabled,v.value,C]),{onNativeOptionAdd:S,onNativeOptionRemove:N}=m;return St(()=>(S(A),()=>N(A)),[S,N,A]),x.jsxs(x.Fragment,{children:[x.jsx(Ge.span,{id:v.textId,...f,ref:E}),v.isSelected&&d.valueNode&&!d.valueNodeHasChildren?$i.createPortal(f.children,d.valueNode):null]})});Tg.displayName=Bi;var Ag="SelectItemIndicator",Rg=y.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a;return wg(Ag,u).isSelected?x.jsx(Ge.span,{"aria-hidden":!0,...o,ref:r}):null});Rg.displayName=Ag;var _s="SelectScrollUpButton",Cg=y.forwardRef((a,r)=>{const u=il(_s,a.__scopeSelect),o=ef(_s,a.__scopeSelect),[s,f]=y.useState(!1),d=lt(r,o.onScrollButtonChange);return St(()=>{if(u.viewport&&u.isPositioned){let p=function(){const m=v.scrollTop>0;f(m)};const v=u.viewport;return p(),v.addEventListener("scroll",p),()=>v.removeEventListener("scroll",p)}},[u.viewport,u.isPositioned]),s?x.jsx(Og,{...a,ref:d,onAutoScroll:()=>{const{viewport:p,selectedItem:v}=u;p&&v&&(p.scrollTop=p.scrollTop-v.offsetHeight)}}):null});Cg.displayName=_s;var Ds="SelectScrollDownButton",Ng=y.forwardRef((a,r)=>{const u=il(Ds,a.__scopeSelect),o=ef(Ds,a.__scopeSelect),[s,f]=y.useState(!1),d=lt(r,o.onScrollButtonChange);return St(()=>{if(u.viewport&&u.isPositioned){let p=function(){const m=v.scrollHeight-v.clientHeight,b=Math.ceil(v.scrollTop)<m;f(b)};const v=u.viewport;return p(),v.addEventListener("scroll",p),()=>v.removeEventListener("scroll",p)}},[u.viewport,u.isPositioned]),s?x.jsx(Og,{...a,ref:d,onAutoScroll:()=>{const{viewport:p,selectedItem:v}=u;p&&v&&(p.scrollTop=p.scrollTop+v.offsetHeight)}}):null});Ng.displayName=Ds;var Og=y.forwardRef((a,r)=>{const{__scopeSelect:u,onAutoScroll:o,...s}=a,f=il("SelectScrollButton",u),d=y.useRef(null),p=ko(u),v=y.useCallback(()=>{d.current!==null&&(window.clearInterval(d.current),d.current=null)},[]);return y.useEffect(()=>()=>v(),[v]),St(()=>{var b;const m=p().find(w=>w.ref.current===document.activeElement);(b=m==null?void 0:m.ref.current)==null||b.scrollIntoView({block:"nearest"})},[p]),x.jsx(Ge.div,{"aria-hidden":!0,...s,ref:r,style:{flexShrink:0,...s.style},onPointerDown:Je(s.onPointerDown,()=>{d.current===null&&(d.current=window.setInterval(o,50))}),onPointerMove:Je(s.onPointerMove,()=>{var m;(m=f.onItemLeave)==null||m.call(f),d.current===null&&(d.current=window.setInterval(o,50))}),onPointerLeave:Je(s.onPointerLeave,()=>{v()})})}),FE="SelectSeparator",IE=y.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a;return x.jsx(Ge.div,{"aria-hidden":!0,...o,ref:r})});IE.displayName=FE;var zs="SelectArrow",e2=y.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=qo(u),f=al(zs,u),d=il(zs,u);return f.open&&d.position==="popper"?x.jsx(qw,{...s,...o,ref:r}):null});e2.displayName=zs;var t2="SelectBubbleInput",Mg=y.forwardRef(({__scopeSelect:a,value:r,...u},o)=>{const s=y.useRef(null),f=lt(o,s),d=Qw(r);return y.useEffect(()=>{const p=s.current;if(!p)return;const v=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(v,"value").set;if(d!==r&&b){const w=new Event("change",{bubbles:!0});b.call(p,r),p.dispatchEvent(w)}},[d,r]),x.jsx(Ge.select,{...u,style:{...Fv,...u.style},ref:f,defaultValue:r})});Mg.displayName=t2;function _g(a){return a===""||a===void 0}function Dg(a){const r=Nl(a),u=y.useRef(""),o=y.useRef(0),s=y.useCallback(d=>{const p=u.current+d;r(p),function v(m){u.current=m,window.clearTimeout(o.current),m!==""&&(o.current=window.setTimeout(()=>v(""),1e3))}(p)},[r]),f=y.useCallback(()=>{u.current="",window.clearTimeout(o.current)},[]);return y.useEffect(()=>()=>window.clearTimeout(o.current),[]),[u,s,f]}function zg(a,r,u){const s=r.length>1&&Array.from(r).every(m=>m===r[0])?r[0]:r,f=u?a.indexOf(u):-1;let d=n2(a,Math.max(f,0));s.length===1&&(d=d.filter(m=>m!==u));const v=d.find(m=>m.textValue.toLowerCase().startsWith(s.toLowerCase()));return v!==u?v:void 0}function n2(a,r){return a.map((u,o)=>a[(r+o)%a.length])}var l2=ug,a2=sg,i2=dg,r2=mg,o2=hg,u2=pg,c2=bg,s2=Eg,f2=Tg,d2=Rg,m2=Cg,h2=Ng;function p2({...a}){return x.jsx(l2,{"data-slot":"select",...a})}function v2({...a}){return x.jsx(i2,{"data-slot":"select-value",...a})}function g2({className:a,size:r="default",children:u,...o}){return x.jsxs(a2,{"data-slot":"select-trigger","data-size":r,className:at("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...o,children:[u,x.jsx(r2,{asChild:!0,children:x.jsx(cv,{className:"size-4 opacity-50"})})]})}function y2({className:a,children:r,position:u="popper",...o}){return x.jsx(o2,{children:x.jsxs(u2,{"data-slot":"select-content",className:at("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",u==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:u,...o,children:[x.jsx(b2,{}),x.jsx(c2,{className:at("p-1",u==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),x.jsx(x2,{})]})})}function Yp({className:a,children:r,...u}){return x.jsxs(s2,{"data-slot":"select-item",className:at("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...u,children:[x.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:x.jsx(d2,{children:x.jsx(uv,{className:"size-4"})})}),x.jsx(f2,{children:r})]})}function b2({className:a,...r}){return x.jsx(m2,{"data-slot":"select-scroll-up-button",className:at("flex cursor-default items-center justify-center py-1",a),...r,children:x.jsx($x,{className:"size-4"})})}function x2({className:a,...r}){return x.jsx(h2,{"data-slot":"select-scroll-down-button",className:at("flex cursor-default items-center justify-center py-1",a),...r,children:x.jsx(cv,{className:"size-4"})})}const S2=Ys("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function w2({className:a,variant:r,asChild:u=!1,...o}){const s=u?sv:"span";return x.jsx(s,{"data-slot":"badge",className:at(S2({variant:r}),a),...o})}const E2=({onSignReport:a})=>{const[r,u]=y.useState([]),[o,s]=y.useState([]),[f,d]=y.useState(!0),[p,v]=y.useState(null),[m,b]=y.useState(""),[w,E]=y.useState("all"),[C,A]=y.useState([]);y.useEffect(()=>{S(),N()},[]),y.useEffect(()=>{O()},[r,m,w]);const S=async()=>{try{d(!0);const z=await fetch("/api/reports");if(!z.ok)throw new Error("Failed to fetch reports");const q=await z.json();u(q)}catch(z){v(z.message)}finally{d(!1)}},N=async()=>{try{const z=await fetch("/api/templates");if(z.ok){const q=await z.json();A(q)}}catch(z){console.error("Failed to fetch templates:",z)}},O=()=>{let z=r;m&&(z=z.filter(q=>q.template_name.toLowerCase().includes(m.toLowerCase())||q.id.toLowerCase().includes(m.toLowerCase()))),w!=="all"&&(z=z.filter(q=>q.template_id===w)),s(z)},M=(z,q)=>{const P=z.download_urls[q];window.open(P,"_blank")},D=z=>new Date(z).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return f?x.jsxs("div",{className:"flex items-center justify-center py-12",children:[x.jsx(qs,{className:"h-8 w-8 animate-spin text-blue-600"}),x.jsx("span",{className:"ml-2 text-gray-600",children:"Loading reports..."})]}):p?x.jsxs("div",{className:"text-center py-12",children:[x.jsxs("div",{className:"text-red-600 mb-4",children:["Error: ",p]}),x.jsx(nt,{onClick:S,variant:"outline",children:"Try Again"})]}):x.jsxs("div",{className:"space-y-6",children:[x.jsxs("div",{className:"text-center",children:[x.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Generated Reports"}),x.jsx("p",{className:"text-gray-600",children:"View, download, and manage your generated reports"})]}),x.jsxs(Ta,{children:[x.jsx(Aa,{children:x.jsxs(Ra,{className:"flex items-center space-x-2",children:[x.jsx(Ix,{className:"h-5 w-5"}),x.jsx("span",{children:"Search & Filter"})]})}),x.jsx(Ca,{children:x.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[x.jsx("div",{className:"flex-1",children:x.jsxs("div",{className:"relative",children:[x.jsx(oS,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),x.jsx(Ev,{placeholder:"Search reports by name or ID...",value:m,onChange:z=>b(z.target.value),className:"pl-10"})]})}),x.jsx("div",{className:"md:w-64",children:x.jsxs(p2,{value:w,onValueChange:E,children:[x.jsx(g2,{children:x.jsx(v2,{placeholder:"Filter by template"})}),x.jsxs(y2,{children:[x.jsx(Yp,{value:"all",children:"All Templates"}),C.map(z=>x.jsx(Yp,{value:z.id,children:z.name},z.id))]})]})})]})})]}),o.length===0?x.jsxs("div",{className:"text-center py-12",children:[x.jsx(Ea,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),x.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:r.length===0?"No Reports Generated":"No Reports Found"}),x.jsx("p",{className:"text-gray-600",children:r.length===0?"Start by creating your first report from a template.":"Try adjusting your search or filter criteria."})]}):x.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(z=>x.jsxs(Ta,{className:"hover:shadow-lg transition-shadow",children:[x.jsx(Aa,{children:x.jsxs("div",{className:"flex items-start justify-between",children:[x.jsxs("div",{children:[x.jsx(Ra,{className:"text-lg",children:z.template_name}),x.jsxs(Gi,{className:"text-sm text-gray-500",children:["ID: ",z.id.substring(0,8),"..."]})]}),x.jsx("div",{className:"flex items-center space-x-1",children:z.e_signed&&x.jsxs(w2,{variant:"secondary",className:"text-xs",children:[x.jsx(Ro,{className:"h-3 w-3 mr-1"}),"Signed"]})})]})}),x.jsxs(Ca,{className:"space-y-4",children:[x.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[x.jsx(Gx,{className:"h-4 w-4 mr-2"}),D(z.created_at)]}),x.jsxs("div",{className:"flex flex-col space-y-2",children:[x.jsxs("div",{className:"flex space-x-2",children:[x.jsxs(nt,{size:"sm",onClick:()=>M(z,"docx"),className:"flex-1",children:[x.jsx(Yi,{className:"h-4 w-4 mr-1"}),"DOCX"]}),x.jsxs(nt,{size:"sm",variant:"outline",onClick:()=>M(z,"pdf"),className:"flex-1",children:[x.jsx(Yi,{className:"h-4 w-4 mr-1"}),"PDF"]})]}),!z.e_signed&&x.jsxs(nt,{size:"sm",variant:"secondary",onClick:()=>a(z),className:"w-full",children:[x.jsx(Ro,{className:"h-4 w-4 mr-2"}),"Add E-Signature"]})]})]})]},z.id))}),x.jsxs("div",{className:"text-center text-sm text-gray-500",children:["Showing ",o.length," of ",r.length," reports"]})]})},T2=({report:a,onClose:r,onSignatureComplete:u})=>{const o=y.useRef(null),[s,f]=y.useState(!1),[d,p]=y.useState(!1),v=C=>{f(!0);const A=o.current,S=A.getBoundingClientRect(),N=C.clientX-S.left,O=C.clientY-S.top,M=A.getContext("2d");M.beginPath(),M.moveTo(N,O)},m=C=>{if(!s)return;const A=o.current,S=A.getBoundingClientRect(),N=C.clientX-S.left,O=C.clientY-S.top,M=A.getContext("2d");M.lineWidth=2,M.lineCap="round",M.strokeStyle="#000",M.lineTo(N,O),M.stroke()},b=()=>{f(!1)},w=()=>{const C=o.current;C.getContext("2d").clearRect(0,0,C.width,C.height)},E=async()=>{p(!0);try{const A=o.current.toDataURL("image/png");if(!(await fetch(`/api/reports/${a.id}/sign`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({signature:A})})).ok)throw new Error("Failed to save signature");u()}catch(C){console.error("Error saving signature:",C),alert("Failed to save signature. Please try again.")}finally{p(!1)}};return x.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:x.jsxs(Ta,{className:"w-full max-w-2xl",children:[x.jsx(Aa,{children:x.jsxs("div",{className:"flex items-center justify-between",children:[x.jsxs("div",{children:[x.jsxs(Ra,{className:"flex items-center space-x-2",children:[x.jsx(Ro,{className:"h-5 w-5"}),x.jsx("span",{children:"Add E-Signature"})]}),x.jsxs(Gi,{children:["Sign the report: ",a.template_name]})]}),x.jsx(nt,{variant:"ghost",size:"sm",onClick:r,children:x.jsx(fS,{className:"h-4 w-4"})})]})}),x.jsxs(Ca,{className:"space-y-4",children:[x.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4",children:[x.jsx("canvas",{ref:o,width:600,height:200,className:"w-full border border-gray-200 rounded cursor-crosshair",onMouseDown:v,onMouseMove:m,onMouseUp:b,onMouseLeave:b}),x.jsx("p",{className:"text-sm text-gray-500 mt-2 text-center",children:"Draw your signature above using your mouse or touch"})]}),x.jsxs("div",{className:"flex justify-between",children:[x.jsxs(nt,{variant:"outline",onClick:w,children:[x.jsx(iS,{className:"h-4 w-4 mr-2"}),"Clear"]}),x.jsxs("div",{className:"space-x-2",children:[x.jsx(nt,{variant:"outline",onClick:r,children:"Cancel"}),x.jsx(nt,{onClick:E,disabled:d,children:d?x.jsxs(x.Fragment,{children:[x.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):x.jsxs(x.Fragment,{children:[x.jsx(uv,{className:"h-4 w-4 mr-2"}),"Save Signature"]})})]})]})]})]})})};function A2(){const[a,r]=y.useState(null),[u,o]=y.useState(!1),[s,f]=y.useState(null),d=b=>{r(b)},p=()=>{r(null)},v=b=>{f(b),o(!0)},m=()=>{o(!1),f(null)};return x.jsx(Tx,{children:x.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[x.jsx(IS,{}),x.jsx("main",{className:"container mx-auto px-4 py-8",children:x.jsxs(ex,{children:[x.jsx(bs,{path:"/",element:x.jsxs("div",{className:"space-y-8",children:[x.jsxs("div",{className:"text-center space-y-4",children:[x.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"Professional Report Generator"}),x.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Create professional reports from templates with ease. Select a template, fill the form, and generate your report in DOCX or PDF format."})]}),x.jsxs("div",{className:"grid md:grid-cols-3 gap-6 mb-8",children:[x.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[x.jsx(Ea,{className:"h-12 w-12 text-blue-600 mb-4"}),x.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Template Selection"}),x.jsx("p",{className:"text-gray-600",children:"Choose from professional templates for various report types"})]}),x.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[x.jsx(Yi,{className:"h-12 w-12 text-green-600 mb-4"}),x.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Multiple Formats"}),x.jsx("p",{className:"text-gray-600",children:"Download your reports in DOCX or PDF format"})]}),x.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[x.jsx(Ro,{className:"h-12 w-12 text-purple-600 mb-4"}),x.jsx("h3",{className:"text-lg font-semibold mb-2",children:"E-Signature"}),x.jsx("p",{className:"text-gray-600",children:"Add digital signatures to your reports"})]})]}),a?x.jsx(u1,{template:a,onFormSubmit:p,onCancel:()=>r(null)}):x.jsx(e1,{onTemplateSelect:d})]})}),x.jsx(bs,{path:"/reports",element:x.jsx(E2,{onSignReport:v})})]})}),u&&x.jsx(T2,{report:s,onClose:()=>o(!1),onSignatureComplete:m})]})})}ub.createRoot(document.getElementById("root")).render(x.jsx(y.StrictMode,{children:x.jsx(A2,{})}));
