{"version": 3, "names": ["_classPrivateFieldGet", "require", "_classExtractFieldDescriptor", "receiver", "privateMap", "classPrivateFieldGet2"], "sources": ["../../src/helpers/classExtractFieldDescriptor.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nimport classPrivateFieldGet2 from \"classPrivateFieldGet2\";\n\nexport default function _classExtractFieldDescriptor(receiver, privateMap) {\n  return classPrivateFieldGet2(privateMap, receiver);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,qBAAA,GAAAC,OAAA;AAEe,SAASC,4BAA4BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACzE,OAAOC,qBAAqB,CAACD,UAAU,EAAED,QAAQ,CAAC;AACpD", "ignoreList": []}