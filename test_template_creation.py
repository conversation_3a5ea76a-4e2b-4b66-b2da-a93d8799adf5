#!/usr/bin/env python3
"""
Create a test DOCX template with placeholders for testing the upload functionality.
"""

from docx import Document
import os

def create_test_template():
    """Create a test DOCX template with various placeholders"""
    doc = Document()
    
    # Add title
    title = doc.add_heading('Test Template Document', 0)
    
    # Add paragraphs with placeholders
    doc.add_paragraph('Employee Name: {{employee_name}}')
    doc.add_paragraph('Department: {{department}}')
    doc.add_paragraph('Date: {{date}}')
    doc.add_paragraph('Salary: {{salary}}')
    
    # Add a table with placeholders
    table = doc.add_table(rows=3, cols=2)
    table.style = 'Table Grid'
    
    # Header row
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Field'
    hdr_cells[1].text = 'Value'
    
    # Data rows
    row1_cells = table.rows[1].cells
    row1_cells[0].text = 'Project'
    row1_cells[1].text = '{{project_name}}'
    
    row2_cells = table.rows[2].cells
    row2_cells[0].text = 'Manager'
    row2_cells[1].text = '{{manager_name}}'
    
    # Add more complex placeholders
    doc.add_paragraph('Complex placeholder: {{user.profile.address.city}}')
    doc.add_paragraph('Another complex one: {{order.items[0].name}}')
    
    # Add header with placeholder
    section = doc.sections[0]
    header = section.header
    header_para = header.paragraphs[0]
    header_para.text = 'Company: {{company_name}}'
    
    # Add footer with placeholder
    footer = section.footer
    footer_para = footer.paragraphs[0]
    footer_para.text = 'Signature: {{signature}}'
    
    # Save the document
    output_path = os.path.join(os.path.dirname(__file__), 'test_template.docx')
    doc.save(output_path)
    print(f"Test template created: {output_path}")
    return output_path

if __name__ == '__main__':
    try:
        create_test_template()
    except Exception as e:
        print(f"Error creating test template: {e}")
        import traceback
        traceback.print_exc()
